{"symbol": "DEUIDXEUR", "timestamp": "2025-06-20T12:48:03.253289", "session_id": "20250620_124803", "feedback": {}, "pattern_count": 1, "profitable_patterns": 1, "performance_insights": {"total_insights": 0, "unique_insights": [], "common_themes": [], "top_insights": []}, "validation_metrics": {"avg_validation_score": 0.95, "max_validation_score": 0.95, "min_validation_score": 0.95, "quality_distribution": {"excellent": 1}, "avg_criteria_met": 7.0, "total_validated_patterns": 1}, "pattern_characteristics": {"execution_speed_distribution": {"medium": 1}, "risk_profile_distribution": {"conservative": 1}, "market_suitability_distribution": {"general": 1}, "avg_trade_volume": 3.0, "total_patterns_analyzed": 1, "dominant_execution_speed": "medium", "dominant_risk_profile": "conservative"}, "market_context": {"session_success_rate": 1.0, "session_quality": "excellent", "total_patterns_discovered": 1, "profitable_patterns_found": 1, "discovery_efficiency": "1/1", "timestamp": "2025-06-20T12:48:03.253302"}, "learning_intelligence": {"strategic_insights": ["High-quality patterns identified: 1/1 patterns scored >0.8 validation", "Conservative bias detected: 1/1 patterns are conservative", "Low sample warning: Average 3 trades per pattern"], "learning_recommendations": ["Prioritize discovering patterns with similar validation characteristics", "Conservative patterns work well for this symbol - prioritize high win rate discovery", "Focus on discovering patterns with larger sample sizes for reliability"], "session_intelligence_score": 6, "analysis_timestamp": "2025-06-20T12:48:03.253307"}}