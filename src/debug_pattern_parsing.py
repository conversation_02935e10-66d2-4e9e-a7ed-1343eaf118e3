#!/usr/bin/env python3
"""
🔍 DEBUG PATTERN PARSING

Test script to debug why patterns are not generating trades.
Tests the complete parsing pipeline from LLM response to executable functions.
"""

import sys
import os
import json
import pandas as pd
import numpy as np
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent))

from backtesting_rule_parser import parse_backtesting_rules, SchemaBasedPatternParser
from config import config

def test_pattern_parsing():
    """Test pattern parsing with real LLM response"""
    
    # Load the most recent LLM session
    llm_data_path = Path("llm_data/GBRIDXGBP/session_20250701_092458.json")
    
    if not llm_data_path.exists():
        print(f"❌ LLM session file not found: {llm_data_path}")
        return
    
    with open(llm_data_path, 'r') as f:
        session_data = json.load(f)
    
    llm_response = session_data['llm_analysis']
    
    print("🔍 DEBUG - LLM Response Analysis:")
    print("=" * 80)
    print(f"Response length: {len(llm_response)} characters")
    print(f"Contains JSON blocks: {'```json' in llm_response}")
    print(f"Number of JSON blocks: {llm_response.count('```json')}")
    print("=" * 80)
    
    # Show first 500 characters
    print("First 500 characters:")
    print(llm_response[:500])
    print("=" * 80)
    
    # Test parsing
    print("\n🔧 Testing Pattern Parsing...")
    try:
        rule_functions = parse_backtesting_rules(llm_response)
        print(f"✅ Parsing successful! Generated {len(rule_functions)} rule functions")
        
        if rule_functions:
            print("\n📊 Testing Rule Functions with Sample Data...")
            
            # Create sample market data
            dates = pd.date_range('2024-01-01', periods=100, freq='1H')
            sample_data = pd.DataFrame({
                'Open': np.random.uniform(1.2500, 1.2600, 100),
                'High': np.random.uniform(1.2510, 1.2610, 100),
                'Low': np.random.uniform(1.2490, 1.2590, 100),
                'Close': np.random.uniform(1.2500, 1.2600, 100),
            }, index=dates)
            
            # Ensure OHLC logic is correct
            for i in range(len(sample_data)):
                high = max(sample_data.iloc[i]['Open'], sample_data.iloc[i]['Close']) + np.random.uniform(0, 0.001)
                low = min(sample_data.iloc[i]['Open'], sample_data.iloc[i]['Close']) - np.random.uniform(0, 0.001)
                sample_data.iloc[i, sample_data.columns.get_loc('High')] = high
                sample_data.iloc[i, sample_data.columns.get_loc('Low')] = low
            
            # Test each rule function
            for i, rule_func in enumerate(rule_functions, 1):
                print(f"\n   🔍 Testing Rule Function {i}...")
                
                signals_generated = 0
                for idx in range(10, len(sample_data)):  # Test on multiple bars
                    try:
                        signal = rule_func(sample_data, idx)
                        if signal:
                            signals_generated += 1
                            print(f"      ✅ Signal generated at index {idx}: {signal}")
                            break  # Just show first signal
                    except Exception as e:
                        print(f"      ❌ Error testing rule function {i} at index {idx}: {e}")
                        break
                
                if signals_generated == 0:
                    print(f"      ⚠️ Rule Function {i} generated no signals in test data")
        
        else:
            print("❌ No rule functions generated")
            
    except Exception as e:
        print(f"❌ Parsing failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Test schema-based parser directly
    print("\n🔧 Testing Schema-Based Parser Directly...")
    try:
        parser = SchemaBasedPatternParser()
        patterns = parser.parse_llm_response(llm_response)
        print(f"✅ Schema parsing successful! Generated {len(patterns)} patterns")
        
        for i, pattern in enumerate(patterns, 1):
            print(f"\n   📋 Pattern {i}: {pattern.pattern_name}")
            print(f"      Entry conditions: {len(pattern.entry_conditions)}")
            for j, condition in enumerate(pattern.entry_conditions):
                print(f"         Condition {j+1}: {condition}")
            print(f"      Exit conditions: {len(pattern.exit_conditions)}")
            for j, condition in enumerate(pattern.exit_conditions):
                print(f"         Exit {j+1}: {condition}")
            print(f"      Entry logic: {pattern.entry_logic}")

            # Test condition evaluation
            print(f"      🔧 Testing condition evaluation...")
            parser_instance = SchemaBasedPatternParser()

            # Create test data for condition evaluation
            test_dates = pd.date_range('2024-01-01', periods=20, freq='1H')
            test_data = pd.DataFrame({
                'Open': [1.25] * 20,
                'High': [1.251] * 20,
                'Low': [1.249] * 20,
                'Close': [1.2505] * 20,
            }, index=test_dates)

            for j, condition in enumerate(pattern.entry_conditions):
                condition_type = condition.get('condition', 'unknown')
                print(f"         Testing condition {j+1} ({condition_type})...")

                # Check if condition is supported
                if condition_type in parser_instance.supported_conditions:
                    print(f"            ✅ Condition type '{condition_type}' is supported")
                    try:
                        result = parser_instance._evaluate_condition(condition, test_data, 10)
                        print(f"            Result: {result}")
                    except Exception as e:
                        print(f"            ❌ Error evaluating condition: {e}")
                else:
                    print(f"            ❌ Condition type '{condition_type}' is NOT supported")
                    print(f"            Available conditions: {list(parser_instance.supported_conditions.keys())}")
            
    except Exception as e:
        print(f"❌ Schema parsing failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_pattern_parsing()
