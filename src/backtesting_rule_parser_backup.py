#!/usr/bin/env python3
"""
🎯 SCHEMA-BASED BACKTESTING RULE PARSER

Parses structured JSON trading patterns from LLM and converts them into executable backtesting functions.
Supports <PERSON>'s situational analysis methodology with robust schema-based pattern processing.

Key Features:
- Parses structured JSON patterns (no more regex hell!)
- Generates Python functions compatible with walk-forward validation
- Handles complex multi-condition entry/exit logic
- Validates patterns against JSON schema
- Extensible condition evaluation system
"""

import json
import re
from dataclasses import dataclass
from typing import List, Optional, Callable, Dict, Any
import pandas as pd
import numpy as np
from pathlib import Path

# Optional config import for testing compatibility
try:
    from config import config
    WALKFORWARD_MIN_MULTIPLIER = config.WALKFORWARD_MIN_MULTIPLIER
except ImportError:
    # Default value for testing
    WALKFORWARD_MIN_MULTIPLIER = 1.5


class BacktestingRuleParseError(Exception):
    """Exception raised when backtesting rule parsing fails"""
    pass

@dataclass
class BacktestingTradingRule:
    """Simplified trading rule for backtesting only"""
    rule_id: int
    name: str
    market_logic: str
    entry_logic: str
    direction: str  # 'long' or 'short'
    stop_logic: str
    target_logic: str
    position_size: float
    timeframe: str = "5min"

class BacktestingRuleParser:
    """Simplified parser for backtesting-only rules"""
    
    def __init__(self):
        self.rules = []
        self.validation_errors = []
    
    def parse_llm_response(self, llm_response: str) -> List[BacktestingTradingRule]:
        """Parse LLM response into backtesting-compatible rules"""
        self.rules = []
        self.validation_errors = []
        
        # Extract individual patterns
        patterns = self._extract_patterns(llm_response)
        
        for i, pattern_text in enumerate(patterns, 1):
            try:
                rule = self._parse_single_pattern(i, pattern_text)
                if rule:
                    self.rules.append(rule)
            except Exception as e:
                self.validation_errors.append(f"Pattern {i} parsing failed: {str(e)}")
        
        if not self.rules and self.validation_errors:
            raise BacktestingRuleParseError(f"All patterns failed to parse: {'; '.join(self.validation_errors)}")
        
        return self.rules
    
    def _extract_patterns(self, llm_response: str) -> List[str]:
        """Extract individual patterns from LLM response"""
        # Look for multiple pattern formats:
        # **PATTERN X:, ### Pattern X:, Pattern X:, ### PATTERN [X]:, ### PATTERN [Y]:
        pattern_matches = re.findall(r'(?:\*\*PATTERN \d+:|###\s*PATTERN\s*\[[XY]\]:|###\s*Pattern \d+:|Pattern \d+:).*?(?=(?:\*\*PATTERN \d+:|###\s*PATTERN\s*\[[XY]\]:|###\s*Pattern \d+:|Pattern \d+:)|\Z)',
                                   llm_response, re.DOTALL | re.IGNORECASE)

        if not pattern_matches:
            raise BacktestingRuleParseError("UNBREAKABLE RULE VIOLATION: No fallback allowed. LLM must provide properly formatted rules.")

        return pattern_matches
    
    def _parse_single_pattern(self, rule_id: int, pattern_text: str) -> Optional[BacktestingTradingRule]:
        """Parse a single pattern into a backtesting rule"""
        
        # Extract pattern name - handle multiple formats
        name_match = re.search(r'(?:\*\*PATTERN \d+:|###\s*Pattern \d+:|Pattern \d+:)\s*([^\*\n#]+)', pattern_text, re.IGNORECASE)
        name = name_match.group(1).strip() if name_match else f"Pattern {rule_id}"
        
        # Extract fields using simple regex
        market_logic = self._extract_field(pattern_text, r'Market Logic:\s*([^\n]+)')
        entry_logic = self._extract_field(pattern_text, r'Entry Logic:\s*([^\n]+)')
        direction = self._extract_field(pattern_text, r'Direction:\s*([^\n]+)')
        stop_logic = self._extract_field(pattern_text, r'Stop Logic:\s*([^\n]+)')
        target_logic = self._extract_field(pattern_text, r'Target Logic:\s*([^\n]+)')
        position_size_str = self._extract_field(pattern_text, r'Position Size:\s*([^\n]+)')
        timeframe = self._extract_field(pattern_text, r'Timeframe:\s*([^\n]+)') or "5min"
        
        # Validate required fields (must be present and non-empty after strip)
        required_fields = [entry_logic, direction, stop_logic, target_logic]
        if not all(f and str(f).strip() for f in required_fields):
            raise BacktestingRuleParseError(f"Missing required fields in pattern {rule_id}")
        
        # Parse position size - must be whole number for backtesting framework compatibility
        try:
            position_size = float(position_size_str) if position_size_str else 1.0
            # Ensure position size is a whole number to avoid backtesting framework rejections
            if position_size != int(position_size):
                print(f"⚠️  Warning: Position size {position_size} rounded to {int(position_size)} (whole numbers only)")
                position_size = max(1.0, float(int(position_size)))
        except (ValueError, TypeError):
            position_size = 1.0
        
        # Normalize direction
        direction = direction.lower().strip()
        if direction not in ['long', 'short']:
            raise BacktestingRuleParseError(f"Invalid direction '{direction}' in pattern {rule_id}")
        
        return BacktestingTradingRule(
            rule_id=rule_id,
            name=name,
            market_logic=market_logic or "",
            entry_logic=entry_logic,
            direction=direction,
            stop_logic=stop_logic,
            target_logic=target_logic,
            position_size=position_size,
            timeframe=timeframe
        )
    
    def _extract_field(self, text: str, pattern: str) -> Optional[str]:
        """Extract a field using regex pattern"""
        match = re.search(pattern, text, re.IGNORECASE)
        return match.group(1).strip() if match else None
    
    def generate_python_functions(self) -> List[Callable]:
        """Generate Python functions for backtesting"""
        functions = []
        
        for rule in self.rules:
            func = self._create_python_function(rule)
            if func:
                functions.append(func)
        
        return functions
    
    def _create_python_function(self, rule: BacktestingTradingRule) -> Optional[Callable]:
        """Create a Python function from a backtesting rule"""
        
        def rule_function(data, current_idx):
            """Generated backtesting rule function"""
            if current_idx < 1 or current_idx >= len(data):
                return None
            
            current = data.iloc[current_idx]
            previous = data.iloc[current_idx - 1]
            
            # Evaluate entry condition
            if not self._evaluate_entry_condition(rule.entry_logic, current, previous):
                return None
            
            # Calculate prices
            entry_price = current['Close']
            stop_price = self._calculate_stop_price(rule.stop_logic, entry_price, current, previous, rule.direction)
            target_price = self._calculate_target_price(rule.target_logic, entry_price, stop_price, rule.direction)
            
            if stop_price is None or target_price is None:
                return None
            
            # Validate order logic (only check direction, not distances)
            # Market orders can execute with any SL/TP distance in real trading
            if rule.direction == 'long':
                if stop_price >= entry_price or target_price <= entry_price:
                    return None
            else:  # short
                if stop_price <= entry_price or target_price >= entry_price:
                    return None
            
            return {
                'entry_price': entry_price,
                'stop_loss': stop_price,
                'take_profit': target_price,
                'direction': rule.direction,
                'position_size': rule.position_size,
                'rule_id': rule.rule_id
            }
        
        return rule_function
    
    def _evaluate_entry_condition(self, entry_logic: str, current: pd.Series, previous: pd.Series) -> bool:
        """Evaluate entry condition with enhanced logic support"""
        # Clean the entry logic - remove backticks and normalize
        entry_logic = entry_logic.strip().replace('`', '').lower()

        # Simple price comparisons
        if 'current_close > previous_high' in entry_logic:
            return current['Close'] > previous['High']
        elif 'current_close < previous_low' in entry_logic:
            return current['Close'] < previous['Low']
        elif 'current_close > previous_close' in entry_logic:
            return current['Close'] > previous['Close']
        elif 'current_close < previous_close' in entry_logic:
            return current['Close'] < previous['Close']

        # Range-based comparisons (volatility expansion/contraction)
        elif 'current_range > previous_range' in entry_logic:
            current_range = current['High'] - current['Low']
            previous_range = previous['High'] - previous['Low']

            # Extract multiplier if present (e.g., "* 1.5")
            import re
            multiplier_match = re.search(r'\*\s*(\d+\.?\d*)', entry_logic)
            multiplier = float(multiplier_match.group(1)) if multiplier_match else 1.0

            return current_range > (previous_range * multiplier)

        # Gap-based comparisons
        elif 'current_open > previous_close' in entry_logic:
            # Extract gap size if present (e.g., "+ 10")
            import re
            gap_match = re.search(r'\+\s*(\d+\.?\d*)', entry_logic)
            gap_size = float(gap_match.group(1)) if gap_match else 0.0

            return current['Open'] > (previous['Close'] + gap_size)

        elif 'current_open < previous_close' in entry_logic:
            # Extract gap size if present (e.g., "- 10")
            import re
            gap_match = re.search(r'-\s*(\d+\.?\d*)', entry_logic)
            gap_size = float(gap_match.group(1)) if gap_match else 0.0

            return current['Open'] < (previous['Close'] - gap_size)

        # Default: try to evaluate as simple comparison
        try:
            # Replace variables with actual values - ensure proper spacing
            logic = entry_logic.replace('current_close', f" {current['Close']} ")
            logic = logic.replace('previous_high', f" {previous['High']} ")
            logic = logic.replace('previous_low', f" {previous['Low']} ")
            logic = logic.replace('previous_close', f" {previous['Close']} ")
            logic = logic.replace('current_high', f" {current['High']} ")
            logic = logic.replace('current_low', f" {current['Low']} ")
            logic = logic.replace('current_open', f" {current['Open']} ")

            # Handle range calculations
            current_range = current['High'] - current['Low']
            previous_range = previous['High'] - previous['Low']
            logic = logic.replace('current_range', f" {current_range} ")
            logic = logic.replace('previous_range', f" {previous_range} ")

            # Clean up extra spaces
            logic = ' '.join(logic.split())

            # Simple evaluation (be careful with eval)
            if '>' in logic or '<' in logic:
                return eval(logic)
        except Exception:
            # Ignore evaluation errors silently
            pass

        return False
    
    def _calculate_stop_price(self, stop_logic: str, entry_price: float, current: pd.Series, previous: pd.Series, direction: str) -> Optional[float]:
        """Calculate stop loss price"""
        # Clean the stop logic - remove backticks and normalize
        stop_logic = stop_logic.strip().replace('`', '').lower()

        if 'previous_low' in stop_logic:
            return previous['Low']
        elif 'previous_high' in stop_logic:
            return previous['High']
        elif 'current_low' in stop_logic:
            return current['Low']
        elif 'current_high' in stop_logic:
            return current['High']

        # Try percentage-based
        pct_match = re.search(r'(\d+\.?\d*)%', stop_logic)
        if pct_match:
            pct = float(pct_match.group(1)) / 100
            if direction == 'long':
                return entry_price * (1 - pct)
            else:
                return entry_price * (1 + pct)

        return None
    
    def _calculate_target_price(self, target_logic: str, entry_price: float, stop_price: Optional[float], direction: str) -> Optional[float]:
        """Calculate target price"""
        # Clean the target logic - remove backticks and normalize
        target_logic = target_logic.strip().replace('`', '').lower()
        
        # Risk-reward based
        if stop_price and ('entry_price + (entry_price - stop_price)' in target_logic or 'entry_price - (stop_price - entry_price)' in target_logic):
            risk = abs(entry_price - stop_price)
            
            # Extract multiplier
            mult_match = re.search(r'\*\s*(\d+\.?\d*)', target_logic)
            multiplier = float(mult_match.group(1)) if mult_match else 2.0  # Default 2:1 ratio

            # Ensure minimum multiplier for profitability
            multiplier = max(multiplier, WALKFORWARD_MIN_MULTIPLIER)
            
            if direction == 'long':
                return entry_price + (risk * multiplier)
            else:
                return entry_price - (risk * multiplier)
        
        # Percentage-based
        pct_match = re.search(r'(\d+\.?\d*)%', target_logic)
        if pct_match:
            pct = float(pct_match.group(1)) / 100
            if direction == 'long':
                return entry_price * (1 + pct)
            else:
                return entry_price * (1 - pct)
        
        return None

# Main functions for compatibility
def parse_backtesting_rules(llm_response: str) -> List[Callable]:
    """Parse LLM response and return backtesting functions"""
    parser = BacktestingRuleParser()
    try:
        parser.parse_llm_response(llm_response)
        return parser.generate_python_functions()
    except BacktestingRuleParseError as e:
        print(f"Backtesting rule parsing failed: {e}")
        return []
