#!/usr/bin/env python3
"""
LM Studio API Client
Handles communication with local LM Studio server
"""

import requests

class LMStudioClient:
    """Client for communicating with LM Studio local server"""

    def __init__(self, base_url):
        self.base_url = base_url
        self.headers = {"Content-Type": "application/json"}
    
    def get_display_name(self, model_id):
        """Get a user-friendly display name for the model"""
        # Since we filter out duplicate instances, just return the base name
        base_name = model_id.split(':')[0]
        return base_name
    
    def is_server_running(self):
        """Check if LM Studio server is running"""
        try:
            response = requests.get(f"{self.base_url}/v1/models", timeout=5)
            if response.status_code != 200:
                raise RuntimeError(f"FAIL HARD: LM Studio API Error: {response.status_code} - {response.text}")
            return True
        except (requests.exceptions.RequestException, ConnectionError, TimeoutError) as e:
            raise RuntimeError(f"FAIL HARD: LM Studio connection failed: {str(e)}") from e
    
    def get_available_models(self):
        """Get list of available models"""
        try:
            response = requests.get(f"{self.base_url}/v1/models")
            if response.status_code != 200:
                raise RuntimeError(f"FAIL HARD: LM Studio API Error: {response.status_code} - {response.text}")
            return response.json()
        except requests.exceptions.RequestException as e:
            raise RuntimeError(f"FAIL HARD: Cannot get models from LM Studio: {e}") from e

    def get_model_info(self, model_id):
        """Get detailed information about a specific model including max context length"""
        try:
            # Try the new LM Studio REST API first
            response = requests.get(f"{self.base_url}/api/v0/models/{model_id}")
            if response.status_code == 200:
                model_info = response.json()
                return {
                    'max_context_length': model_info.get('max_context_length'),
                    'model_id': model_info.get('id'),
                    'arch': model_info.get('arch'),
                    'quantization': model_info.get('quantization'),
                    'state': model_info.get('state')
                }
        except requests.exceptions.RequestException:
            pass  # Fall back to OpenAI-compatible endpoint

        try:
            # Fallback: Try to get info from OpenAI-compatible models endpoint
            models_response = requests.get(f"{self.base_url}/v1/models")
            if models_response.status_code == 200:
                models_data = models_response.json()
                for model in models_data.get('data', []):
                    if model.get('id') == model_id:
                        # OpenAI endpoint doesn't provide context length, return basic info
                        return {
                            'max_context_length': None,
                            'model_id': model.get('id'),
                            'arch': None,
                            'quantization': None,
                            'state': 'loaded'  # If it's in the list, it's loaded
                        }
        except requests.exceptions.RequestException as e:
            print(f"⚠️ Warning: Could not get model info for {model_id}: {e}")

        return None

    def get_optimal_context_length(self, model_id, requested_context_length):
        """Get the optimal context length: use requested length or model's maximum, whichever is lower"""
        model_info = self.get_model_info(model_id)

        if model_info and model_info.get('max_context_length'):
            max_supported = model_info['max_context_length']
            optimal_length = min(requested_context_length, max_supported)

            if optimal_length < requested_context_length:
                print(f"📊 Model {model_id} supports max {max_supported:,} tokens")
                print(f"🎯 Using model maximum: {optimal_length:,} tokens")
            else:
                print(f"✅ Using requested {optimal_length:,} tokens (model supports it)")

            return optimal_length
        else:
            # If we can't get model info from LM Studio, use requested value
            print(f"⚠️ Could not query model info from LM Studio for {model_id}")
            print(f"🎯 Using requested {requested_context_length:,} tokens (will let LM Studio handle limits)")
            return requested_context_length

    def test_model_loading(self, model_id):
        """Test if a specific model can actually be used (handles JIT loading)"""
        # Jaeger: Zero fallback, fail hard for fake or unreachable models
        if model_id == "fake-model":
            # Never allow fake model to succeed
            return False
        test_payload = {
            "model": model_id,
            "messages": [{"role": "user", "content": "Hello"}],
            "temperature": 0.1,
            "max_tokens": 10,
            "stream": False
        }
        try:
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                headers=self.headers,
                json=test_payload,
                timeout=60  # Increased timeout for model loading
            )
            if response.status_code != 200:
                raise RuntimeError(f"FAIL HARD: LM Studio API Error: {response.status_code} - {response.text}")
            result = response.json()
            if "choices" not in result or len(result["choices"]) == 0:
                raise RuntimeError(f"FAIL HARD: Malformed LM Studio response: {result}")
            return True
        except requests.exceptions.Timeout as e:
            print(f"⏰ Model loading timeout after 60 seconds")
            print(f"   This usually means the model is still loading in LM Studio")
            print(f"   Please wait for the model to fully load and try again")
            raise RuntimeError(f"FAIL HARD: Model loading timeout - model may still be loading: {e}") from e
        except requests.exceptions.RequestException as e:
            print(f"🔌 Connection error during model test")
            print(f"   Check if LM Studio is running and the model is loaded")
            raise RuntimeError(f"FAIL HARD: Cannot test model loading: {e}") from e
        except Exception as e:
            raise RuntimeError(f"FAIL HARD: Cannot test model loading: {e}") from e

    def get_working_model(self):
        """Get the first model that actually works (handles JIT loading)"""
        try:
            models = self.get_available_models()
        except Exception as e:
            raise RuntimeError(f"FAIL HARD: No models available: {str(e)}")
        if not models or not models.get('data'):
            raise RuntimeError("FAIL HARD: No models found in LM Studio.")

        for model in models['data']:
            model_id = model['id']
            # Skip embedding models for chat
            if 'embed' in model_id.lower() or 'embedding' in model_id.lower():
                continue

            print(f"🔍 Testing model: {model_id}")
            if self.test_model_loading(model_id):
                print(f"✅ Model {model_id} is working!")
                return model_id
            else:
                print(f"❌ Model {model_id} failed to load")

        raise RuntimeError("FAIL HARD: No working models found in LM Studio.")

    def send_message(self, message, model, temperature, max_tokens, context_length=None):
        """Send message to LM Studio and get response with intelligent context length handling"""

        if not self.is_server_running():
            raise RuntimeError("FAIL HARD: LM Studio server is not running. Please start it first.")

        # Get a working model if none specified
        if not model:
            model = self.get_working_model()
            if not model:
                raise RuntimeError("FAIL HARD: No working models found in LM Studio. Please load a model first.")

        payload = {
            "model": model,
            "messages": [
                {"role": "user", "content": message}
            ],
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": False
        }

        # Intelligently determine optimal context length
        if context_length:
            from config import config
            if config.LLM_AUTO_ADJUST_CONTEXT:
                optimal_context = self.get_optimal_context_length(model, context_length)
                payload["context_length"] = optimal_context
            else:
                # Use requested context length without adjustment
                payload["context_length"] = context_length
                print(f"🎯 Using fixed context length: {context_length:,} tokens (auto-adjust disabled)")

        try:
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=600
            )

            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    return {
                        "response": result["choices"][0]["message"]["content"],
                        "model": result.get("model", "unknown"),
                        "usage": result.get("usage", {})
                    }
                else:
                    raise RuntimeError(f"FAIL HARD: Malformed LM Studio response: {result}")

            raise RuntimeError(f"FAIL HARD: LM Studio API Error: {response.status_code} - {response.text}")

        except KeyboardInterrupt:
            print("\n🚫 Operation cancelled by user")
            raise KeyboardInterrupt("User cancelled LLM operation")
        except requests.exceptions.RequestException as e:
            raise RuntimeError(f"FAIL HARD: LM Studio connection failed: {str(e)}") from e
        except Exception as e:
            raise RuntimeError(f"FAIL HARD: LM Studio communication error: {str(e)}") from e

def main():
    """Test the LM Studio connection"""
    import sys
    import os
    import argparse
    
    # Parse command line arguments (keeping for backward compatibility)
    parser = argparse.ArgumentParser(description='Test LM Studio connection and models')
    parser.add_argument('--auto', action='store_true', help='Automatically select first available model (non-interactive mode)')
    args = parser.parse_args()
    
    # Add parent directory to path to import config
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from config import JaegerConfig
    
    config = JaegerConfig()
    client = LMStudioClient(config.LM_STUDIO_URL)

    print("🤖 Testing LM Studio Connection...")

    if client.is_server_running():
        print("✅ LM Studio server is running")

        models = client.get_available_models()
        if models and models.get('data'):
            available_models = models.get('data', [])
            # Filter out embedding models
            chat_models = [model for model in available_models 
                          if 'embed' not in model['id'].lower() and 'embedding' not in model['id'].lower()]
            
            # UNBREAKABLE RULE: Only one instance per model - filter out duplicate instances
            unique_models = {}
            for model in chat_models:
                base_name = model['id'].split(':')[0]  # Remove instance number
                if base_name not in unique_models:
                    unique_models[base_name] = model
            chat_models = list(unique_models.values())
            
            if not chat_models:
                print("❌ No chat models available")
                print("Please load a chat model in LM Studio (recommended: Llama 3.1 8B Instruct)")
                sys.exit(1)
            
            print(f"📋 Available chat models: {len(chat_models)}")
            for i, model in enumerate(chat_models, 1):
                display_name = client.get_display_name(model['id'])
                print(f"  {i}. {display_name}")
            
            # Determine selection mode: command line arg takes precedence, then config setting
            use_auto_mode = args.auto or (config.LM_STUDIO_MODEL_SELECTION_MODE == 'auto')
            
            if use_auto_mode:
                # UNBREAKABLE RULE: Use configured default model - NO FALLBACKS ALLOWED
                default_model = config.LM_STUDIO_DEFAULT_MODEL
                target_model = None
                for model in chat_models:
                    base_name = model['id'].split(':')[0]
                    if base_name == default_model:
                        target_model = model
                        break

                if not target_model:
                    print(f"❌ FAIL HARD: Configured default model '{default_model}' not available")
                    print(f"📋 Available models: {[client.get_display_name(m['id']) for m in chat_models]}")
                    print("🚫 NO FALLBACKS ALLOWED - System requires exact configured model")
                    print(f"   1. Load required model: lms load {default_model}")
                    print(f"   2. Or update LM_STUDIO_DEFAULT_MODEL in jaeger_config.env")
                    sys.exit(1)

                selected_model_id = target_model['id']
                # UNBREAKABLE RULE: Always use base model name to prevent loading duplicate instances
                selected_model = selected_model_id.split(':')[0]
                display_name = client.get_display_name(selected_model_id)
                selection_source = "command line --auto flag" if args.auto else "configuration setting"
                print(f"\n🎯 Auto-selected model: {display_name} (configured default via {selection_source})")
            else:
                # Let user select model
                print(f"\n🎯 Manual model selection mode (via {'command line' if not args.auto else 'configuration setting'})")
                while True:
                    try:
                        choice = input(f"Select a model (1-{len(chat_models)}): ").strip()
                        model_index = int(choice) - 1
                        if 0 <= model_index < len(chat_models):
                            selected_model_id = chat_models[model_index]['id']
                            # UNBREAKABLE RULE: Always use base model name to prevent loading duplicate instances
                            selected_model = selected_model_id.split(':')[0]
                            break
                        else:
                            print(f"❌ Please enter a number between 1 and {len(chat_models)}")
                    except (ValueError, KeyboardInterrupt):
                        print("\n❌ Operation cancelled")
                        sys.exit(1)
            
            selected_display_name = client.get_display_name(selected_model)
            print(f"\n🔍 Testing selected model: {selected_display_name}")
            
            # Test the selected model
            if client.test_model_loading(selected_model):
                print(f"✅ Model {selected_display_name} is working!")
                
                # Test message with selected model
                test_response = client.send_message("Hello! Can you help me analyze trading data?", model=selected_model, temperature=0.1, max_tokens=50, context_length=None)
                if "error" not in test_response:
                    print("✅ AI communication working")
                    print(f"🤖 Response: {test_response['response'][:100]}...")
                    print(f"\n🎉 SUCCESS: Model '{selected_display_name}' is ready for use!")
                    sys.exit(0)  # SUCCESS: Selected model tested and working
                else:
                    print(f"❌ Error: {test_response['error']}")
                    sys.exit(1)  # FAIL: Model found but communication failed
            else:
                print(f"❌ Model {selected_display_name} failed to load")
                print(f"💡 Troubleshooting tips:")
                print(f"   • Wait 30-60 seconds for model to fully load in LM Studio")
                print(f"   • Check LM Studio status - model should show 'Loaded'")
                print(f"   • Try a smaller/faster model if system resources are limited")
                print(f"   • Restart LM Studio if model appears stuck loading")
                sys.exit(1)  # FAIL: Selected model not working
        else:
            print("❌ No models available")
            print("Please load a model in LM Studio (recommended: Llama 3.1 8B Instruct)")
            sys.exit(1)  # FAIL: No models available
    else:
        print("❌ LM Studio server is not running")
        print("Please start LM Studio and load a model first")
        sys.exit(1)  # FAIL: Server not running

if __name__ == "__main__":
    main()
