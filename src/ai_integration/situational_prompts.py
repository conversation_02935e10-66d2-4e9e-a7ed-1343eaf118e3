#!/usr/bin/env python3
"""
🎯 SITUATIONAL ANALYSIS PROMPTS - TWO-STAGE DISCOVERY SYSTEM

Revolutionary two-stage pattern discovery system:
Stage 1: <PERSON> methodology for sophisticated pattern discovery
Stage 2: Translation to backtesting-compatible format

This module implements Stage 1: Enhanced Discovery using <PERSON>'s proven methodology.
"""

class TomHougaardDiscoveryPrompts:
    """Generate Tom Ho<PERSON> methodology prompts for sophisticated pattern discovery"""
    
    @staticmethod
    def get_tom_hougaard_core_principles():
        """Core Tom <PERSON> principles for CFD PROFIT pattern discovery"""
        return [
            "Focus on identifying recurring PROFITABLE market situations in CFD trading with 1:100 leverage",
            "Develop statistical frameworks based on what actually GENERATES PROFITS in leveraged markets",
            "Think in terms of 'When X situation occurs, Y PROFITABLE outcome follows with Z frequency'",
            "Remember: 'Anything can happen' on any individual trade, but PROFITABLE patterns provide statistical edges over time",
            "Use ONLY Open, High, Low, Close data for MAXIMUM PROFIT clarity - no indicators whatsoever",
            "Rely on human pattern recognition focused on PROFIT-GENERATING market context understanding",
            "Focus on geometric relationships and price behavior patterns that CREATE PROFITS in CFD trading",
            "Analyze 'naked charts' to see what price is actually doing to GENERATE MAXIMUM PROFITS",
            "Every pattern must be designed to exploit 1:100 leverage for AMPLIFIED PROFIT GENERATION"
        ]
    
    @staticmethod
    def get_discovery_methodology_steps():
        """Tom Hougaard's discovery methodology steps"""
        return [
            "Conduct extensive historical chart analysis spanning multiple years",
            "Examine thousands of trading sessions manually (not algorithmic backtesting)",
            "Look for recurring price behaviors that happen with measurable frequency",
            "Cross-correlate patterns across different market conditions and timeframes",
            "Document pattern frequency and success rates through manual tracking",
            "Identify patterns that occur with consistent frequency (minimum hundreds of occurrences)",
            "Calculate actual win rates and risk-reward ratios",
            "Validate patterns across different market regimes (trending, ranging, volatile, quiet)",
            "Ensure patterns maintain edge across extended time periods",
            "Aim for statistical significance (60%+ win rates for viable patterns)"
        ]
    
    @staticmethod
    def get_situational_context_requirements():
        """Situational context integration requirements"""
        return [
            "Understand WHEN patterns work best (specific market hours, days, conditions)",
            "Recognize that patterns are situational, not mechanical",
            "Identify market memory effects (how markets react to previous important levels)",
            "Consider multi-timeframe context and trend alignment",
            "Analyze market regime sensitivity (bull/bear markets, high/low volatility periods)",
            "Investigate opening behaviors, closing behaviors, specific hour tendencies",
            "Analyze multi-day relationships (Monday-Friday correlations, week-to-week patterns)",
            "Explore intraday volatility cycles and their exploitable characteristics",
            "Look for seasonal or cyclical behavioral patterns"
        ]
    
    @staticmethod
    def get_pattern_categories_to_explore():
        """Pattern categories for exploration using Tom Hougaard methodology"""
        return {
            "Time-Based Behavioral Patterns": [
                "Opening behaviors, closing behaviors, specific hour tendencies",
                "Multi-day relationships (Monday-Friday correlations, week-to-week patterns)",
                "Intraday volatility cycles and their exploitable characteristics",
                "Seasonal or cyclical behavioral patterns"
            ],
            "Geometric Price Relationships": [
                "New harmonic or measured move relationships",
                "Novel support/resistance formation patterns",
                "Unique breakout or breakdown configurations",
                "New reversal or continuation formations"
            ],
            "Market Memory and Context Patterns": [
                "How markets react to previous important price levels",
                "Gap behavior and follow-through patterns",
                "Range expansion and contraction cycles",
                "Multi-session price relationships"
            ],
            "Volatility and Momentum Patterns": [
                "Unique volatility expansion/contraction signals",
                "Momentum exhaustion patterns",
                "Acceleration and deceleration signatures",
                "Volume-price relationship patterns (if volume data available)"
            ]
        }
    
    @staticmethod
    def get_discovery_examples():
        """Tom Hougaard-style discovery examples to inspire thinking"""
        return [
            "When volatility contracts after 3 consecutive higher closes, and the 4th day opens below previous close but recovers above the 2nd day's high within 2 hours, participants typically drive price to test recent highs within 24 hours due to institutional FOMO and retail momentum chasing",
            "During London-NY overlap sessions, when institutional flows create specific price behaviors where the market makes a new session high but fails to hold above the previous day's close, retail participants typically panic-sell creating a predictable retracement to the session VWAP",
            "After 3 consecutive sessions where the daily range contracts by more than 20% each day, and volume decreases correspondingly, the 4th session typically sees a volatility expansion of 150%+ as accumulated institutional positions trigger algorithmic breakouts",
            "When Thursday closes higher than Friday's high from the previous week, and Monday gaps down but recovers above Thursday's close within the first 2 hours, participants typically exhibit continuation behavior driving price to test the previous week's high within 48 hours",
            "During regime transitions from high to low volatility, when the market creates 5+ consecutive inside days followed by an outside day that closes in the upper 25% of its range, institutional participants typically continue the directional bias for 3-5 sessions",
            "When the market opens within 10 pips of the previous session's close for 3 consecutive sessions, then gaps beyond this range on the 4th session, retail participants typically chase the gap direction creating momentum that persists for the entire session"
        ]
    
    @staticmethod
    def generate_stage1_discovery_prompt(ohlc_data, market_summaries="", performance_feedback=""):
        """
        Generate Stage 1: Tom Hougaard Discovery Prompt
        
        This prompt focuses purely on sophisticated pattern discovery using Tom Hougaard's methodology.
        No constraints on format - pure creative discovery.
        
        Args:
            ohlc_data: Market data for context
            market_summaries: Behavioral intelligence summaries
            performance_feedback: Previous session learning data
            
        Returns:
            Stage 1 discovery prompt string
        """
        
        # Enforce strict OHLCV capitalization and fail-fast if missing
        required_cols = ['Open', 'High', 'Low', 'Close']
        missing = [col for col in required_cols if col not in ohlc_data.columns]
        if missing:
            raise RuntimeError(f'UNBREAKABLE RULE VIOLATION: Missing OHLC columns: {missing}')
        
        core_principles = TomHougaardDiscoveryPrompts.get_tom_hougaard_core_principles()
        methodology_steps = TomHougaardDiscoveryPrompts.get_discovery_methodology_steps()
        context_requirements = TomHougaardDiscoveryPrompts.get_situational_context_requirements()
        pattern_categories = TomHougaardDiscoveryPrompts.get_pattern_categories_to_explore()
        discovery_examples = TomHougaardDiscoveryPrompts.get_discovery_examples()
        
        # Add market regime analysis
        regime_analysis = TomHougaardDiscoveryPrompts._analyze_market_regime(ohlc_data)
        
        prompt = f"""🎯 STAGE 1: SOPHISTICATED CFD PATTERN DISCOVERY USING TOM HOUGAARD METHODOLOGY

You are a master CFD pattern detective using Tom Hougaard's proven methodology to discover completely novel, HIGHLY PROFITABLE trading patterns. Your mission is to uncover hidden statistical edges in pure price action data that generate MAXIMUM PROFITABILITY in CFD trading with 1:100 leverage.

💰 CRITICAL FOCUS: PROFITABILITY IS EVERYTHING
This is CFD trading with 1:100 leverage. Every pattern you discover MUST be designed for MAXIMUM PROFIT GENERATION. Patterns that don't generate substantial profits are worthless. Focus exclusively on patterns that can deliver consistent, high-probability profits in leveraged CFD trading.

🧠 CORE CFD TRADING PHILOSOPHY TO ADOPT:
{chr(10).join([f'• {principle}' for principle in core_principles])}

💰 CFD TRADING SPECIFICS - MANDATORY CONSIDERATIONS:
• This is CFD trading with 1:100 leverage - small price movements create large profits/losses
• Focus on patterns that exploit leverage effectively for maximum profit generation
• Consider the amplified risk/reward dynamics of leveraged trading
• Patterns must be designed for quick, profitable entries and exits
• Every pattern MUST have clear profit targets that justify the leveraged risk

📊 DISCOVERY METHODOLOGY TO FOLLOW:
{chr(10).join([f'• {step}' for step in methodology_steps])}

🎯 SITUATIONAL CONTEXT REQUIREMENTS:
{chr(10).join([f'• {req}' for req in context_requirements])}

📈 MARKET REGIME ANALYSIS:
{regime_analysis}

🔍 ENHANCED BEHAVIORAL ANALYSIS:
{market_summaries}

📚 LEARNING FROM PREVIOUS SESSIONS:
{performance_feedback}

🎨 PATTERN CATEGORIES TO EXPLORE:
"""
        
        for category, patterns in pattern_categories.items():
            prompt += f"\n**{category}:**\n"
            for pattern in patterns:
                prompt += f"  • {pattern}\n"
        
        prompt += f"""
🌟 DISCOVERY EXAMPLES TO INSPIRE YOUR THINKING:
(These are examples of the TYPE of sophisticated thinking to use - create your own original patterns)

{chr(10).join([f'• {example}' for example in discovery_examples])}

🎯 YOUR CFD PROFITABILITY DISCOVERY MISSION:

Discover 3-5 completely NOVEL, HIGHLY PROFITABLE CFD trading patterns that:

1. **MAXIMIZE PROFITABILITY** - Every pattern must be designed for maximum profit generation in CFD trading
2. **Exploit 1:100 leverage effectively** - Patterns must take advantage of leveraged trading dynamics
3. **Have never been documented before** - These should be your original profit-focused discoveries
4. **Follow Tom Hougaard's pure price action principles** - No indicators, only OHLC data for maximum profit clarity
5. **Are statistically validated for PROFIT** - Show clear frequency, success rate, and PROFIT POTENTIAL
6. **Are situationally contextualized for PROFIT** - Explain when and why they generate maximum profits
7. **Demonstrate clear profit edge** - Explain the statistical and logical basis for PROFIT generation
8. **Exploit specific behavioral inefficiencies for PROFIT** - Focus on participant psychology that creates profit opportunities

🔬 CFD PROFITABILITY DISCOVERY OUTPUT FORMAT:

For each HIGHLY PROFITABLE pattern you discover, provide:

**PATTERN [X]: [Descriptive Name] - CFD PROFIT MAXIMIZER**
Market Situation: [Detailed description of the specific market situation this pattern exploits for MAXIMUM PROFIT]
Participant Behavior: [Explain WHY participants behave predictably in this situation creating PROFIT OPPORTUNITIES - the psychological/institutional drivers that generate profits]
Statistical Edge: [Explain the statistical basis for PROFIT GENERATION - frequency, win rate potential, risk-reward characteristics optimized for CFD leverage]
Multi-Timeframe Context: [Describe the timeframe relationships and context requirements that maximize PROFITABILITY]
Market Memory Effects: [How does this pattern relate to previous price levels and market memory to generate PROFITS]
Optimal Conditions: [Specific market conditions, times, or regimes where this pattern generates MAXIMUM PROFITS]
CFD Leverage Advantage: [How this pattern specifically exploits 1:100 leverage for amplified profit generation]
Profit Expectations: [Expected profit targets, win rates, and profit frequency for this pattern in CFD trading]
Behavioral Logic: [The deeper behavioral/institutional logic that creates this predictable response]

🚨 CRITICAL REQUIREMENTS:

• Focus on DISCOVERY, not implementation - be creative and sophisticated
• Each pattern must exploit a specific behavioral inefficiency
• Patterns must be based on recurring market situations, not one-off events
• Include multi-timeframe context and situational awareness
• Explain the participant psychology that creates the edge
• Ensure patterns align with the market regime analysis provided above
• Think like Tom Hougaard - situational, behavioral, statistical

Remember: You are not copying existing patterns but discovering new ones using Tom Hougaard's proven discovery methodology. Think like a market detective, using statistical analysis and situational awareness to uncover hidden edges in pure price action data.

Begin your sophisticated pattern discovery now:"""

        return prompt
    
    @staticmethod
    def _analyze_market_regime(ohlc_data):
        """Analyze market regime to guide pattern discovery"""
        try:
            # Calculate basic regime metrics
            total_return = ((ohlc_data['Close'].iloc[-1] / ohlc_data['Close'].iloc[0]) - 1) * 100
            volatility = ohlc_data['Close'].pct_change().std() * 100
            
            # Determine regime and bias
            if total_return > 5 and volatility > 2:
                regime = "STRONG UPTREND"
                bias = "🚀 BULLISH MOMENTUM - Focus on continuation patterns and breakout strategies"
                pattern_guidance = "Look for momentum continuation patterns, institutional accumulation signals"
            elif total_return < -5 and volatility > 2:
                regime = "STRONG DOWNTREND"
                bias = "🐻 BEARISH MOMENTUM - Focus on breakdown patterns and short-side opportunities"
                pattern_guidance = "Look for momentum breakdown patterns, institutional distribution signals"
            elif volatility < 1:
                regime = "LOW VOLATILITY"
                bias = "😴 CONSOLIDATION - Focus on range-bound patterns and volatility expansion signals"
                pattern_guidance = "Look for compression patterns, volatility breakout setups"
            elif volatility > 3:
                regime = "HIGH VOLATILITY"
                bias = "⚡ VOLATILE - Focus on mean reversion and volatility contraction patterns"
                pattern_guidance = "Look for overextension patterns, volatility normalization signals"
            else:
                regime = "BALANCED"
                bias = "⚖️ NEUTRAL - Use both directional and mean reversion approaches"
                pattern_guidance = "Look for balanced patterns that work in multiple market conditions"
            
            return f"""
📊 Market Regime: {regime} ({total_return:+.2f}% total return)
📊 Volatility Level: {volatility:.2f}% (daily price movement)
{bias}
🧠 Discovery Focus: {pattern_guidance}

⚠️ CRITICAL: Align your pattern discovery with this regime analysis.
   Focus on patterns that exploit the current market behavioral dynamics."""
        
        except Exception as e:
            return f"Market regime analysis unavailable: {e}"
