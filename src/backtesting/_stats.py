"""Statistics computation for backtesting results."""

import numpy as np
import pandas as pd
from typing import List, Any


def dummy_stats():
    """Return dummy stats series for optimization."""
    return pd.Series({
        'Start': pd.Timestamp('2020-01-01'),
        'End': pd.Timestamp('2020-12-31'),
        'Duration': pd.Timedelta(days=365),
        'Exposure Time [%]': 100.0,
        'Equity Final [$]': 10000.0,
        'Equity Peak [$]': 10000.0,
        'Return [%]': 0.0,
        'Buy & Hold Return [%]': 0.0,
        'Return (Ann.) [%]': 0.0,
        'Volatility (Ann.) [%]': 0.0,
        'CAGR [%]': 0.0,
        'Sharpe Ratio': 0.0,
        'Sortino Ratio': 0.0,
        'Calmar Ratio': 0.0,
        'Alpha [%]': 0.0,
        'Beta': 0.0,
        'Max. Drawdown [%]': 0.0,
        'Avg. Drawdown [%]': 0.0,
        'Max. Drawdown Duration': pd.<PERSON>delta(days=0),
        'Avg. Drawdown Duration': pd.Timedelta(days=0),
        '# Trades': 0,
        'Win Rate [%]': 0.0,
        'Best Trade [%]': 0.0,
        'Worst Trade [%]': 0.0,
        'Avg. Trade [%]': 0.0,
        'Max. Trade Duration': pd.Timedelta(days=0),
        'Avg. Trade Duration': pd.Timedelta(days=0),
        'Profit Factor': 0.0,
        'Expectancy [%]': 0.0,
        'SQN': 0.0,
        'Kelly Criterion': 0.0,
    })


def compute_stats(trades: List[Any], equity: np.ndarray, ohlc_data: pd.DataFrame, 
                  risk_free_rate: float = 0.0, strategy_instance=None) -> pd.Series:
    """Compute comprehensive trading statistics."""
    
    if not trades:
        stats = dummy_stats()
        stats['Start'] = ohlc_data.index[0]
        stats['End'] = ohlc_data.index[-1]
        stats['Duration'] = ohlc_data.index[-1] - ohlc_data.index[0]
        stats['_strategy'] = strategy_instance
        stats['_equity_curve'] = pd.DataFrame({'Equity': equity}, index=ohlc_data.index)
        stats['_trades'] = pd.DataFrame()
        return stats
    
    # Convert trades to DataFrame
    trades_data = []
    for trade in trades:
        trades_data.append({
            'Size': trade.size,
            'EntryBar': trade.entry_bar,
            'ExitBar': trade.exit_bar,
            'EntryPrice': trade.entry_price,
            'ExitPrice': trade.exit_price,
            'PnL': trade.pl,
            'ReturnPct': trade.pl_pct,
            'EntryTime': trade.entry_time,
            'ExitTime': trade.exit_time,
            'Duration': (trade.exit_time - trade.entry_time) if trade.exit_time else pd.Timedelta(0),
        })
    
    trades_df = pd.DataFrame(trades_data)
    
    # Basic stats
    start_date = ohlc_data.index[0]
    end_date = ohlc_data.index[-1]
    duration = end_date - start_date
    
    initial_cash = equity[0]
    final_equity = equity[-1]
    peak_equity = np.max(equity)
    
    total_return = (final_equity - initial_cash) / initial_cash * 100
    
    # Buy & Hold return
    buy_hold_return = (ohlc_data['Close'].iloc[-1] - ohlc_data['Close'].iloc[0]) / ohlc_data['Close'].iloc[0] * 100
    
    # Annualized return
    years = duration.days / 365.25
    annual_return = (final_equity / initial_cash) ** (1 / years) - 1 if years > 0 else 0
    annual_return_pct = annual_return * 100
    
    # CAGR
    cagr = annual_return_pct
    
    # Volatility (annualized)
    returns = pd.Series(equity).pct_change().dropna()
    volatility = returns.std() * np.sqrt(252) * 100  # Assuming daily data
    
    # Sharpe Ratio
    excess_return = annual_return - risk_free_rate
    sharpe = excess_return / (volatility / 100) if volatility > 0 else 0
    
    # Drawdown calculations
    equity_series = pd.Series(equity)
    peak = equity_series.expanding().max()
    drawdown = (equity_series - peak) / peak * 100
    max_drawdown = drawdown.min()
    avg_drawdown = drawdown[drawdown < 0].mean() if (drawdown < 0).any() else 0
    
    # Trade statistics
    num_trades = len(trades_df)
    winning_trades = trades_df[trades_df['PnL'] > 0]
    win_rate = len(winning_trades) / num_trades * 100 if num_trades > 0 else 0
    
    best_trade = trades_df['ReturnPct'].max() if num_trades > 0 else 0
    worst_trade = trades_df['ReturnPct'].min() if num_trades > 0 else 0
    avg_trade = trades_df['ReturnPct'].mean() if num_trades > 0 else 0
    
    # Trade durations
    if num_trades > 0:
        max_duration = trades_df['Duration'].max()
        avg_duration = trades_df['Duration'].mean()
    else:
        max_duration = pd.Timedelta(0)
        avg_duration = pd.Timedelta(0)
    
    # Profit Factor
    gross_profit = winning_trades['PnL'].sum() if len(winning_trades) > 0 else 0
    gross_loss = abs(trades_df[trades_df['PnL'] < 0]['PnL'].sum()) if (trades_df['PnL'] < 0).any() else 0
    profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf') if gross_profit > 0 else 0
    
    # Expectancy
    expectancy = avg_trade
    
    # SQN (System Quality Number)
    if num_trades > 1:
        sqn = np.sqrt(num_trades) * trades_df['ReturnPct'].mean() / trades_df['ReturnPct'].std()
    else:
        sqn = 0
    
    # Kelly Criterion (simplified)
    if win_rate > 0 and win_rate < 100:
        avg_win = winning_trades['ReturnPct'].mean() if len(winning_trades) > 0 else 0
        avg_loss = abs(trades_df[trades_df['PnL'] < 0]['ReturnPct'].mean()) if (trades_df['PnL'] < 0).any() else 0
        kelly = (win_rate/100) - ((100-win_rate)/100) * (avg_loss/avg_win) if avg_win > 0 else 0
    else:
        kelly = 0
    
    # Sortino Ratio (simplified)
    downside_returns = returns[returns < 0]
    downside_std = downside_returns.std() * np.sqrt(252) if len(downside_returns) > 0 else 0
    sortino = excess_return / (downside_std / 100) if downside_std > 0 else 0
    
    # Calmar Ratio
    calmar = annual_return_pct / abs(max_drawdown) if max_drawdown < 0 else 0
    
    # Exposure time (simplified)
    exposure_time = 100.0  # Assume full exposure for now
    
    # Alpha and Beta (simplified - would need benchmark data)
    alpha = 0.0
    beta = 0.0
    
    # Drawdown durations (simplified)
    max_dd_duration = pd.Timedelta(days=0)
    avg_dd_duration = pd.Timedelta(days=0)
    
    stats = pd.Series({
        'Start': start_date,
        'End': end_date,
        'Duration': duration,
        'Exposure Time [%]': exposure_time,
        'Equity Final [$]': final_equity,
        'Equity Peak [$]': peak_equity,
        'Return [%]': total_return,
        'Buy & Hold Return [%]': buy_hold_return,
        'Return (Ann.) [%]': annual_return_pct,
        'Volatility (Ann.) [%]': volatility,
        'CAGR [%]': cagr,
        'Sharpe Ratio': sharpe,
        'Sortino Ratio': sortino,
        'Calmar Ratio': calmar,
        'Alpha [%]': alpha,
        'Beta': beta,
        'Max. Drawdown [%]': max_drawdown,
        'Avg. Drawdown [%]': avg_drawdown,
        'Max. Drawdown Duration': max_dd_duration,
        'Avg. Drawdown Duration': avg_dd_duration,
        '# Trades': num_trades,
        'Win Rate [%]': win_rate,
        'Best Trade [%]': best_trade,
        'Worst Trade [%]': worst_trade,
        'Avg. Trade [%]': avg_trade,
        'Max. Trade Duration': max_duration,
        'Avg. Trade Duration': avg_duration,
        'Profit Factor': profit_factor,
        'Expectancy [%]': expectancy,
        'SQN': sqn,
        'Kelly Criterion': kelly,
        '_strategy': strategy_instance,
        '_equity_curve': pd.DataFrame({'Equity': equity}, index=ohlc_data.index),
        '_trades': trades_df,
    })
    
    return stats
