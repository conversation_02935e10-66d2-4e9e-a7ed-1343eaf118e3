"""Utility functions and classes for backtesting framework."""

from typing import Callable
import numpy as np
import pandas as pd


def _as_str(value) -> str:
    """Convert value to string representation."""
    if isinstance(value, (int, float)):
        return str(value)
    return str(value)


def try_(func: Callable, exception=Exception, default=None):
    """Try to execute function, return default on exception."""
    try:
        return func()
    except exception:
        return default


def _tqdm(iterable, **kwargs):
    """Simple progress bar replacement."""
    try:
        from tqdm import tqdm
        return tqdm(iterable, **kwargs)
    except ImportError:
        return iterable


def _batch(iterable, n=1):
    """Batch iterable into chunks of size n."""
    from itertools import islice
    iterator = iter(iterable)
    while True:
        chunk = list(islice(iterator, n))
        if not chunk:
            break
        yield chunk


def patch(obj, attr, value):
    """Context manager to temporarily patch object attribute."""
    class Patcher:
        def __init__(self, obj, attr, value):
            self.obj = obj
            self.attr = attr
            self.new_value = value
            self.old_value = None
            
        def __enter__(self):
            self.old_value = getattr(self.obj, self.attr, None)
            setattr(self.obj, self.attr, self.new_value)
            return self
            
        def __exit__(self, exc_type, exc_val, exc_tb):
            if self.old_value is not None:
                setattr(self.obj, self.attr, self.old_value)
            else:
                delattr(self.obj, self.attr)
    
    return Patcher(obj, attr, value)


class _Array(np.ndarray):
    """Enhanced numpy array with pandas Series accessor."""
    
    def __new__(cls, array, index=None):
        obj = np.asarray(array).view(cls)
        obj._index = index
        return obj
    
    def __array_finalize__(self, obj):
        if obj is None:
            return
        self._index = getattr(obj, '_index', None)
    
    @property
    def s(self) -> pd.Series:
        """Return as pandas Series."""
        return pd.Series(self, index=self._index)


class _Indicator(_Array):
    """Indicator array with plotting options."""
    
    def __new__(cls, array, name=None, plot=True, overlay=None, color=None, scatter=False, index=None):
        obj = _Array.__new__(cls, array, index)
        obj._opts = {
            'name': name,
            'plot': plot,
            'overlay': overlay,
            'color': color,
            'scatter': scatter
        }
        return obj


class _Data:
    """Data container for OHLCV data with array access."""
    
    def __init__(self, df: pd.DataFrame):
        self._df = df.copy()
        self._length = len(df)
        
        # Create array views
        for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
            if col in df.columns:
                setattr(self, col, _Array(df[col].values, df.index))
        
        self.index = df.index
        
    def _set_length(self, length: int):
        """Set the visible length of data arrays."""
        self._length = length
        for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
            if hasattr(self, col):
                array = getattr(self, col)
                # Create a view with limited length
                limited_array = _Array(array[:length], self.index[:length])
                setattr(self, col, limited_array)
    
    def _update(self):
        """Update data after modifications."""
        pass
    
    @property
    def df(self) -> pd.DataFrame:
        """Return as DataFrame."""
        return self._df.iloc[:self._length]
    
    @property
    def pip(self) -> float:
        """Smallest price increment."""
        return 0.0001  # Default for forex
    
    def __len__(self):
        return self._length


def _strategy_indicators(strategy):
    """Get strategy indicators."""
    indicators = []
    for attr_name in dir(strategy):
        attr = getattr(strategy, attr_name)
        if isinstance(attr, _Indicator):
            indicators.append((attr_name, attr))
    return indicators


def _indicator_warmup_nbars(strategy):
    """Calculate warmup bars needed for indicators."""
    max_warmup = 0
    for _, indicator in _strategy_indicators(strategy):
        if hasattr(indicator, '_warmup'):
            max_warmup = max(max_warmup, indicator._warmup)
    return max_warmup


class SharedMemoryManager:
    """Placeholder for shared memory management."""
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass
    
    def df2shm(self, df):
        """Convert DataFrame to shared memory."""
        return df  # Simplified
    
    @staticmethod
    def shm2df(shm):
        """Convert shared memory to DataFrame."""
        return shm, []  # Simplified
