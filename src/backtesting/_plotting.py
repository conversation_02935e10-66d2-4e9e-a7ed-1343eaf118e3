"""Plotting functionality for backtesting results."""

import warnings
from typing import Union


def set_bokeh_output(*args, **kwargs):
    """Set bokeh output configuration."""
    try:
        from bokeh.io import output_file, output_notebook
        # Simple implementation
        pass
    except ImportError:
        warnings.warn("Bokeh not available for plotting")


def plot(results=None, df=None, indicators=None, filename=None, plot_width=None,
         plot_equity=True, plot_return=False, plot_pl=True, plot_volume=True,
         plot_drawdown=False, plot_trades=True, smooth_equity=False,
         relative_equity=True, superimpose: Union[bool, str] = True,
         resample=True, reverse_indicators=False, show_legend=True,
         open_browser=True):
    """
    Plot backtest results.
    
    This is a simplified plotting function. In a full implementation,
    this would create interactive Bokeh plots with candlestick charts,
    equity curves, indicators, and trade markers.
    """
    try:
        import matplotlib.pyplot as plt
        import pandas as pd
        
        if results is None or df is None:
            print("No results or data to plot")
            return
        
        # Create plot using matplotlib
        fig, axes = plt.subplots(2, 1, figsize=(12, 8))
        
        # Plot price data
        if 'Close' in df.columns:
            axes[0].plot(df.index, df['Close'], label='Close Price')
            axes[0].set_title('Price Chart')
            axes[0].legend()
        
        # Plot equity curve
        if hasattr(results, '_equity_curve') and plot_equity:
            equity_data = results._equity_curve
            if 'Equity' in equity_data.columns:
                axes[1].plot(equity_data.index, equity_data['Equity'], label='Equity')
                axes[1].set_title('Equity Curve')
                axes[1].legend()
        
        plt.tight_layout()
        
        if filename:
            plt.savefig(filename)
            import os
            if not os.path.exists(filename):
                raise AssertionError(f"UNBREAKABLE RULE VIOLATION: Plot file not created: {filename}")
            print(f"Plot saved to {filename}")
        
        if open_browser:
            plt.show()
        
        return fig
        
    except ImportError:
        print("Matplotlib not available for plotting")
        return None


def plot_heatmaps(heatmap, agg='max', ncols=3, filename='', plot_width=1200, open_browser=True):
    """
    Plot parameter optimization heatmaps.
    
    This is a simplified version. A full implementation would create
    interactive heatmap grids using Bokeh.
    """
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns
        import pandas as pd
        
        if heatmap.empty:
            print("No heatmap data to plot")
            return
        
        # Simple heatmap using seaborn
        plt.figure(figsize=(10, 8))
        
        # Convert MultiIndex to pivot table for heatmap
        if isinstance(heatmap.index, pd.MultiIndex):
            # Take first two levels for 2D heatmap
            if len(heatmap.index.levels) >= 2:
                df_pivot = heatmap.reset_index().pivot(
                    index=heatmap.index.names[0],
                    columns=heatmap.index.names[1],
                    values=heatmap.name or 'value'
                )
                sns.heatmap(df_pivot, annot=True, fmt='.2f', cmap='viridis')
                plt.title('Parameter Optimization Heatmap')
        
        if filename:
            plt.savefig(filename)
            print(f"Heatmap saved to {filename}")
        
        if open_browser:
            plt.show()
            
    except ImportError:
        print("Matplotlib/Seaborn not available for plotting heatmaps")
        return None
