![<PERSON><PERSON><PERSON> Logo](../branding/jaeger-logo.png)

# 🧠 CORTEX - The Central AI Orchestrator V3.0

## 🚀 **VERSION 3.0 REVOLUTIONARY UPGRADE**

### **Two-Stage Discovery Orchestration**
Cortex V3.0 now orchestrates a revolutionary two-stage pattern discovery system:

1. **Stage 1**: <PERSON> methodology for sophisticated pattern discovery
2. **Stage 2**: Translation to backtesting-compatible format
3. **Existing Pipeline**: Preserved validation, walk-forward testing, and MT4 conversion

This approach unleashes LLM creativity while maintaining system reliability.

## 🚀 **VERSION 2.0 FOUNDATION (Enhanced in V3.0)**

### **Enhanced Two-Stage Orchestration Flow (V3.0):**
```
Cortex → Stage 1: Tom Ho<PERSON> Discovery → Stage 2: Backtesting Translation → Walk-Forward Validation → Hard-coded MT4 → Files
```

### **Revolutionary Changes in v3.0:**
- **SOPHISTICATED**: Stage 1 uses <PERSON> methodology for advanced pattern discovery
- **CREATIVE**: Unconstrained LLM creativity in discovery phase
- **RELIABLE**: Stage 2 translation ensures backtesting compatibility
- **PRESERVED**: All existing validation and conversion pipeline maintained
- **ENHANCED**: Dramatically improved pattern discovery capabilities

### **Foundation from v2.0 (Enhanced):**
- **SIMPLIFIED**: Backtesting-compatible output from Stage 2 translation
- **ENHANCED**: Integrated walk-forward validation pipeline
- **IMPROVED**: Deterministic MT4 conversion without LLM dependency
- **MAINTAINED**: All existing orchestration capabilities

## 🎯 Overview

**Cortex** is the central AI orchestrator class in the Jaeger system, located in `src/cortex.py`. It serves as the primary coordinator between the LLM and all Python components, orchestrating the complete autonomous workflow from market data analysis to trading system generation.

**Version 2.0** introduces a streamlined backtesting-only architecture that eliminates dual-format parsing complexity while maintaining all existing quality features.

## 🏗️ Architecture Role

### **Primary Responsibilities:**
- **🤖 LLM Communication** - Manages all interactions with LM Studio
- **🧠 Learning Coordination** - Manages 7-dimensional cross-session pattern learning system
- **📊 Data Orchestration** - Coordinates enhanced multi-dimensional data flow between components
- **🔍 Sophisticated Pattern Discovery** - Autonomous 7-dimensional situational pattern identification
- **✅ Validation Coordination** - Manages backtesting with enhanced 7-dimensional feedback generation
- **📁 System Generation** - Creates complete trading systems and MT4 EAs
- **💾 Session Management** - Stores and retrieves sophisticated 7-dimensional learning data per symbol

### **Component Coordination:**
```
Cortex
├── LMStudioClient           # Direct LLM communication
├── SituationalAnalysisPrompts   # Specialized prompt generation for LLM
├── JaegerStrategy           # CLEAN backtesting.py integration
├── SituationalValidator     # Pattern validation
└── LLMFactChecker          # Response validation
```

## 🔄 Orchestration Flow

### **Main Workflow (`discover_patterns()`):**

1. **🔧 System Initialization**
   - Check LLM availability
   - Initialize all component connections
   - Validate configuration

2. **📊 Data Preparation**
   - Load and validate market data
   - Generate multi-timeframe datasets
   - Prepare OHLC data structures

3. **🧠 AI Analysis Phase**
   - Load previous session insights for learning loop
   - Direct LLM situational pattern discovery with historical context
   - Pure LLM analysis via specialized prompts enhanced with previous feedback
   - Response validation and fact-checking

4. **⚙️ Rule Processing**
   - Parse LLM responses into executable rules
   - Generate MT4 EA code
   - Validate rule syntax and logic

5. **📈 Backtesting & Validation**
   - Test each rule with situational backtesting
   - Apply 7-criteria validation system
   - Filter for profitable patterns only

6. **📁 System Generation**
   - Create complete trading system documentation
   - Generate MT4 Expert Advisor files
   - Save processed data and logs
   - Store session learning data for future improvement

## 🎛️ Key Methods

### **Core Orchestration:**
- `discover_patterns(data_file)` - Main autonomous discovery workflow
- `_autonomous_llm_analysis()` - Coordinates LLM pattern discovery
- `_load_and_prepare_data()` - Data preparation and validation
- `_generate_trading_system()` - Final system generation

### **Analysis Coordination:**
- `_generate_timeframe_data()` - Multi-timeframe data preparation
- `_validate_llm_response()` - Response validation and fact-checking

### **7-Dimensional Learning System Management:**
- `_load_previous_feedback()` - Loads last 100 sessions with 7-dimensional insights per symbol
- `_save_llm_feedback()` - Stores sophisticated multi-dimensional pattern insights for future sessions
- `_cleanup_old_sessions()` - Maintains session limit and cleans old data
- `_generate_performance_feedback_context()` - Creates rich 7-dimensional learning context for LLM
- **Enhanced Feedback Generation** - Captures regime, session, momentum, volume, timeframe, and execution insights

### **🚀 COMPREHENSIVE MULTI-DIMENSIONAL CAPABILITIES:**
- **Multi-timeframe analysis** - Generates 7 timeframes (5min→1w) with behavioral context
- **Behavioral pattern discovery** - Candle position, day/hour effects, previous candle analysis
- **Cross-timeframe relationships** - Setup timeframe → execution timeframe mapping
- **Sophisticated LLM prompting** - Guides toward complex multi-dimensional patterns
- **Timeframe-aware rule parsing** - Patterns specify their optimal execution timeframes

### **Performance Optimization:**
- **Comprehensive timeframe generation** - All timeframes with rich behavioral context
- **Multi-dimensional summaries** - Time-based, position-based, and relationship analysis
- **Optimized backtesting** - 1-minute precision with timeframe-aware rule execution

## 🔗 Integration Points

### **LLM Integration:**
```python
# Cortex coordinates LLM communication
response = self.ai_client.send_message(
    prompt, 
    temperature=config.LLM_TEMPERATURE, 
    max_tokens=config.LLM_MAX_TOKENS
)
```

### **🧠 Comprehensive Multi-Dimensional LLM Analysis:**
```python
# Cortex generates comprehensive behavioral summaries for all timeframes
summaries_str = self._generate_enhanced_timeframe_summaries(timeframe_data)

# Comprehensive prompting guides LLM toward sophisticated pattern discovery
prompt = SituationalAnalysisPrompts.generate_situational_discovery_prompt(
    ohlc_data=ohlc_data,  # Original data for basic context statistics
    profit_context="",  # No pre-screening - let LLM discover patterns
    market_summaries=summaries_str  # Rich multi-dimensional behavioral analysis of ALL timeframes
)

# Target pattern example provided to LLM:
# "During high volatility regime, on Wednesday mornings 9-11am, when 15min and 1h
#  timeframes align bullish, breakouts above previous 5min high with volume
#  confirmation succeed 73% of the time."
```

### **🎯 7 Advanced Pattern Discovery Enhancements:**

#### **Enhanced Behavioral Context Generation:**
```python
# ENHANCEMENT 1: Market Regime Context
data = self._add_market_regime_context(data, timeframe)

# ENHANCEMENT 2: Momentum Persistence Analysis
data = self._add_momentum_persistence_context(data)

# ENHANCEMENT 3: Volume-Price Relationships
if 'volume' in data.columns:
    data = self._add_volume_price_context(data)

# ENHANCEMENT 4: Session Transition Behavior
data = self._add_session_transition_context(data)

# ENHANCEMENT 5: Failure Pattern Analysis
data = self._add_failure_pattern_context(data)

# ENHANCEMENT 7: Price Level Clustering
data = self._add_price_clustering_context(data)
```

#### **Enhanced Summary Generation:**
Each timeframe now includes sophisticated analysis across 7 dimensions:
- **🌊 Market Regime Analysis** - Volatility and trend classification
- **⚡ Momentum Persistence Analysis** - Continuation vs reversal patterns
- **📊 Volume-Price Relationship** - Confirmation and divergence patterns
- **🕐 Session Transition Behavior** - London/NY sessions and overlaps
- **❌ Failure Pattern Analysis** - Breakout success/failure prediction
- **🎯 Price Level Clustering** - Significant price zone identification
- **🔗 Multi-Timeframe Alignment** - Cross-timeframe agreement analysis

#### **LLM vs MT4 EA Distinction:**
- **LLM Discovery**: Uses all 7 enhancements for sophisticated pattern discovery
- **MT4 EA Execution**: Translates to simple conditions (time filters, price comparisons)
- **Smart Translation**: Complex analysis → Simple executable rules

### **Backtesting Integration:**
```python
# Cortex coordinates CLEAN backtesting.py integration
bt = Backtest(ohlc_data, JaegerStrategy)
stats = bt.run(pattern_text=pattern_text)
comprehensive_results = tester.run_with_detailed_analysis(config)
```

## 🚀 Autonomous Operation

### **No User Input Required:**
- Cortex operates completely autonomously
- Processes all data files automatically
- Makes intelligent decisions about pattern viability
- Generates complete trading systems without intervention

### **Clean Architecture:**
- **Single file**: `src/cortex.py` contains the complete orchestrator
- **No fallbacks**: Explicit configuration dependency (no hidden fallback behavior)
- **Clear naming**: File name matches class name following Python conventions

### **Intelligent Filtering:**
- Only generates systems for profitable patterns
- Applies **TRUE dynamic validation** with zero hardcoded thresholds
- Simple rule: If total PnL > 0, pattern is accepted
- Accepts **any profitable combination** regardless of how profit is achieved

### **🎯 TRUE Dynamic Validation System:**
- **Simple profitability check** - Sum all R-multiples, accept if > 0
- **No hardcoded values** - Zero fixed thresholds or minimum standards
- **Universal compatibility** - Same rule works for any instrument or timeframe
- **Maximum success rate** - Will find profitable patterns in any market condition
- **Zero configuration** - No parameters to tune or adjust

## 📊 Output Generation

### **Generated Files (in organized symbol folders):**
- `[SYMBOL]_trading_system_[timestamp].md` - Complete analysis and rules for specific symbol
- `Gipsy_Danger_XXX.mq4` - Analog MT4 Expert Advisor with pattern toggles (sequential: 001, 002, 003...)
- `jaeger.log` - Comprehensive system logs (in project root)

### **Symbol-Based Organization Benefits:**
- **🎯 Symbol-focused** - Easy to find results by trading instrument
- **🔧 Pattern control** - Individual pattern enable/disable in MT4 EAs
- **💰 Profit-focused** - Organization matches what matters to traders
- **📊 Clear identification** - Immediately know which symbol each file relates to

### **Performance Metrics:**
- Total patterns discovered
- Profitable patterns identified
- Validation success rates
- System generation statistics

## 🔧 Configuration

Cortex respects all configuration parameters from `config.py`:
- LLM communication settings
- Validation thresholds
- Backtesting parameters
- File output preferences

## 🎯 Design Philosophy

**Cortex embodies the principle of intelligent orchestration:**
- **Autonomous** - Requires no user intervention
- **Comprehensive** - Handles the complete workflow
- **Intelligent** - Makes quality decisions automatically
- **Reliable** - Includes extensive error handling and validation
- **Fail-Hard Compliant** - Enhanced LLM connectivity validation and error handling
- **Extensible** - Easy to add new components and capabilities

## 🛡️ Enhanced Error Handling Architecture (2025-06-30)

### **Fail-Hard LLM Integration**
Cortex now implements comprehensive fail-hard error handling for all LLM interactions:

#### **Startup Validation**
- **Pre-execution Check**: Validates LLM connectivity before any processing begins
- **Clear Error Messages**: "FAIL HARD:" prefixed messages with specific failure reasons
- **System Halt**: Complete termination when LLM unavailable (no fallbacks)
- **Troubleshooting Guidance**: Step-by-step instructions for resolving LLM issues

#### **Stage-by-Stage Validation**
- **Stage 1 Validation**: Comprehensive error checking for pattern discovery phase
- **Stage 2 Validation**: Complete validation for pattern translation phase
- **Response Quality Checks**: Validates LLM response length and content adequacy
- **Error Propagation**: All errors properly chained and logged as FATAL

#### **Eliminated Fallback Mechanisms**
- **Removed Stub Methods**: Eliminated all temporary directory fallback code
- **No Silent Failures**: System never continues with invalid LLM responses
- **Binary Operation**: System either works perfectly or fails completely
- **Zero Tolerance**: No graceful degradation or partial functionality

---

**Cortex is the brain of the Jaeger system - the central intelligence that coordinates all components to deliver autonomous, high-quality trading system discovery.**
