import os
import pytest
import pandas as pd
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))
from backtesting._broker import _Broker

REAL_DATA_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), 'RealTestData', 'dax_500_bars.csv'))

@pytest.fixture(scope='module')
def real_market_data():
    if not os.path.exists(REAL_DATA_PATH):
        raise FileNotFoundError("UNBREAKABLE RULE VIOLATION: Real market data not found at {}".format(REAL_DATA_PATH))
    df = pd.read_csv(REAL_DATA_PATH)
    required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
    for col in required_cols:
        if col not in df.columns:
            raise AssertionError(f"UNBREAKABLE RULE VIOLATION: Missing required column: {col}")
    return df

def make_broker(df):
    return _Broker(
        data=df,
        cash=100000,
        spread=1.0,
        commission=(0, 0),
        margin=0.01,
        trade_on_close=False,
        hedging=False,
        exclusive_orders=False,
        index=df.index
    )

def test_broker_initialization(real_market_data):
    broker = make_broker(real_market_data)
    assert broker._cash == 100000
    assert broker._spread == 1.0
    assert broker._leverage == 100.0
    assert broker._trade_on_close is False
    assert broker._hedging is False
    assert broker._exclusive_orders is False
    assert hasattr(broker, 'orders')
    assert hasattr(broker, 'trades')
    assert hasattr(broker, 'closed_trades')
