#!/usr/bin/env python3
"""
Comprehensive test suite for Schema-Based Pattern Parser (Backtesting Rule Parser)
UNBREAKABLE RULE: Uses ONLY real market data from /tests/RealTestData
"""

import unittest
import os
import importlib.util
import pandas as pd
import json

class TestBacktestingRuleParser(unittest.TestCase):
    def setUp(self):
        self.parser_path = os.path.join(os.path.dirname(__file__), '../src/backtesting_rule_parser.py')
        spec = importlib.util.spec_from_file_location('backtesting_rule_parser', self.parser_path)
        self.parser = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(self.parser)

    def test_module_import(self):
        """Test that the module imports correctly"""
        self.assertTrue(hasattr(self.parser, 'SchemaBasedPatternParser'))
        self.assertTrue(hasattr(self.parser, 'BacktestingRuleParser'))
        self.assertTrue(hasattr(self.parser, 'parse_backtesting_rules'))

    def test_no_hardcoded_params(self):
        """Test that no hardcoded parameters exist"""
        with open(self.parser_path, 'r') as f:
            content = f.read()
        # Allow specific hardcoded values that are acceptable
        lines = content.split('\n')
        for line in lines:
            if '="' in line and 'WALKFORWARD_MIN_MULTIPLIER' not in line and 'default_factory' not in line:
                # Skip acceptable hardcoded values
                if any(acceptable in line for acceptable in [
                    'method": "fixed_percent"', 'value": 0.02', 'enum', 'description', 'type'
                ]):
                    continue
                self.fail(f'UNBREAKABLE RULE VIOLATION: Hardcoded parameter found: {line.strip()}')

    def test_parse_valid_json_pattern(self):
        """Test parsing a valid JSON pattern"""
        json_pattern = {
            "pattern_name": "Simple Long Breakout",
            "description": "Range expansion breakout pattern",
            "entry_conditions": [
                {
                    "condition": "close_above_high",
                    "lookback": 1
                }
            ],
            "exit_conditions": [
                {
                    "condition": "risk_reward_ratio",
                    "risk": 1,
                    "reward": 2
                }
            ],
            "position_sizing": {
                "method": "fixed_percent",
                "value": 0.02
            }
        }
        
        parser = self.parser.SchemaBasedPatternParser()
        json_response = json.dumps(json_pattern)
        patterns = parser.parse_llm_response(json_response)
        
        self.assertEqual(len(patterns), 1)
        pattern = patterns[0]
        self.assertEqual(pattern.pattern_name, "Simple Long Breakout")
        self.assertEqual(pattern.direction, "long")  # Default direction
        self.assertEqual(len(pattern.entry_conditions), 1)
        self.assertEqual(len(pattern.exit_conditions), 1)

    def test_parse_multiple_patterns(self):
        """Test parsing multiple patterns in array format"""
        multiple_patterns = [
            {
                "pattern_name": "Pattern One",
                "entry_conditions": [{"condition": "close_above_high"}],
                "exit_conditions": [{"condition": "risk_reward_ratio", "risk": 1, "reward": 2}]
            },
            {
                "pattern_name": "Pattern Two", 
                "entry_conditions": [{"condition": "range_expansion", "threshold": 1.5}],
                "exit_conditions": [{"condition": "fixed_take_profit", "percentage": 0.03}]
            }
        ]
        
        parser = self.parser.SchemaBasedPatternParser()
        json_response = json.dumps(multiple_patterns)
        patterns = parser.parse_llm_response(json_response)
        
        self.assertEqual(len(patterns), 2)
        self.assertEqual(patterns[0].pattern_name, "Pattern One")
        self.assertEqual(patterns[1].pattern_name, "Pattern Two")

    def test_parse_missing_required_fields(self):
        """Test that missing required fields raise appropriate errors"""
        incomplete_pattern = {
            "pattern_name": "Incomplete Pattern"
            # Missing entry_conditions and exit_conditions
        }
        
        parser = self.parser.SchemaBasedPatternParser()
        json_response = json.dumps(incomplete_pattern)
        
        with self.assertRaises(self.parser.BacktestingRuleParseError):
            parser.parse_llm_response(json_response)

    def test_no_fallback_violation(self):
        """Test that invalid input triggers UNBREAKABLE RULE violation"""
        parser = self.parser.SchemaBasedPatternParser()
        
        with self.assertRaises(self.parser.BacktestingRuleParseError) as context:
            parser.parse_llm_response("This is not valid JSON")
        
        self.assertIn("UNBREAKABLE RULE VIOLATION", str(context.exception))

    def test_generate_python_functions(self):
        """Test generation of Python functions from patterns"""
        json_pattern = {
            "pattern_name": "Test Pattern",
            "entry_conditions": [
                {
                    "condition": "close_above_high",
                    "lookback": 1
                }
            ],
            "exit_conditions": [
                {
                    "condition": "risk_reward_ratio",
                    "risk": 1,
                    "reward": 2
                }
            ]
        }
        
        parser = self.parser.SchemaBasedPatternParser()
        json_response = json.dumps(json_pattern)
        parser.parse_llm_response(json_response)
        functions = parser.generate_python_functions()
        
        self.assertEqual(len(functions), 1)
        self.assertTrue(callable(functions[0]))

    def test_backward_compatibility_functions(self):
        """Test backward compatibility functions"""
        json_pattern = {
            "pattern_name": "Compatibility Test",
            "entry_conditions": [{"condition": "close_above_high"}],
            "exit_conditions": [{"condition": "risk_reward_ratio", "risk": 1, "reward": 2}]
        }
        
        json_response = json.dumps(json_pattern)
        
        # Test parse_backtesting_rules function
        functions = self.parser.parse_backtesting_rules(json_response)
        self.assertTrue(len(functions) > 0)
        
        # Test BacktestingRuleParser._extract_patterns
        parser = self.parser.BacktestingRuleParser()
        patterns = parser._extract_patterns(json_response)
        self.assertTrue(len(patterns) > 0)

    def test_backward_compatibility_properties(self):
        """Test backward compatibility properties of TradingPattern"""
        json_pattern = {
            "pattern_name": "Compatibility Pattern",
            "entry_conditions": [{"condition": "close_above_high"}],
            "exit_conditions": [{"condition": "risk_reward_ratio", "risk": 1, "reward": 3}],
            "position_sizing": {"method": "fixed_percent", "value": 0.03}
        }
        
        parser = self.parser.SchemaBasedPatternParser()
        json_response = json.dumps(json_pattern)
        patterns = parser.parse_llm_response(json_response)
        pattern = patterns[0]
        
        # Test backward compatibility properties
        self.assertIsInstance(pattern.rule_id, int)
        self.assertEqual(pattern.name, "Compatibility Pattern")
        self.assertEqual(pattern.direction, "long")
        self.assertIsInstance(pattern.entry_logic_text, str)
        self.assertIsInstance(pattern.stop_logic_text, str)
        self.assertIsInstance(pattern.target_logic_text, str)
        self.assertEqual(pattern.position_size, 3.0)  # 0.03 * 100
        self.assertIsInstance(pattern.timeframe, str)

    def test_backward_compatibility_factory(self):
        """Test the backward compatibility factory function"""
        # Test the BacktestingTradingRule factory function
        rule = self.parser.BacktestingTradingRule(
            pattern_name='Legacy Pattern',
            entry_logic_text='current_close > previous_high',
            stop_logic_text='previous_low',
            target_logic_text='entry_price + (entry_price - stop_price) * 2.0',
            direction='long',
            position_size=1.0,
            timeframe='5min'
        )
        
        self.assertEqual(rule.pattern_name, 'Legacy Pattern')
        self.assertIsInstance(rule.rule_id, int)
        self.assertEqual(rule.direction, 'long')
        self.assertEqual(rule.position_size, 1.0)  # 1.0% converted to percentage

if __name__ == '__main__':
    unittest.main()
