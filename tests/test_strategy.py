import os
import pytest
import pandas as pd
from backtesting.backtesting import Strategy, Position
from backtesting._broker import _Broker

REAL_DATA_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), 'RealTestData', 'dax_500_bars.csv'))

@pytest.fixture(scope='module')
def real_market_data():
    if not os.path.exists(REAL_DATA_PATH):
        raise FileNotFoundError("UNBREAKABLE RULE VIOLATION: Real market data not found at {}".format(REAL_DATA_PATH))
    df = pd.read_csv(REAL_DATA_PATH)
    required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
    for col in required_cols:
        if col not in df.columns:
            raise AssertionError(f"UNBREAKABLE RULE VIOLATION: Missing required column: {col}")
    return df

def make_broker(df):
    return _Broker(
        data=df,
        cash=100000,
        spread=1.0,
        commission=(0, 0),
        margin=0.01,
        trade_on_close=False,
        hedging=False,
        exclusive_orders=False,
        index=df.index
    )

class MinimalStrategy(Strategy):
    def init(self):
        pass
    def next(self):
        pass

def test_strategy_instantiation_and_repr(real_market_data):
    broker = make_broker(real_market_data)
    strategy = MinimalStrategy(broker, broker._data, params={})
    r = repr(strategy)
    assert 'Strategy' in r
    assert isinstance(str(strategy), str)
    assert hasattr(strategy, 'init')
    assert hasattr(strategy, 'next')
    assert hasattr(strategy, '_broker')
    assert hasattr(strategy, '_data')
    assert hasattr(strategy, '_params')

def test_strategy_equity_property(real_market_data):
    broker = make_broker(real_market_data)
    strategy = MinimalStrategy(broker, broker._data, params={})
    eq = strategy.equity
    assert isinstance(eq, (int, float))
    assert eq == broker.equity
