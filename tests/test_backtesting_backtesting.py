import unittest
import pandas as pd
import sys
import os

# Ensure src is in sys.path for direct test execution
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))

import backtesting.backtesting  # Explicit import for coverage
try:
    from backtesting._backtest import Backtest
    import backtesting.backtesting as bt_mod
    from backtesting.backtesting import Strategy, Position, Order, Trade
except ImportError as e:
    raise ImportError("FAIL-FAST: Could not import Backtest or Strategy. Ensure src/ is in PYTHONPATH and all modules are built.\n" + str(e))

class TestBacktestingModuleDirect(unittest.TestCase):
    def setUp(self):
        self.df = pd.read_csv('tests/RealTestData/dax_5000_bars.csv')
        if 'DateTime' in self.df.columns:
            self.df['DateTime'] = pd.to_datetime(self.df['DateTime'])
            self.df = self.df.set_index('DateTime')
        required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
        for col in required_cols:
            if col not in self.df.columns:
                raise AssertionError(f"UNBREAKABLE RULE VIOLATION: Missing required column: {col}")
        if self.df[['Open','High','Low','Close']].isnull().any().any():
            raise AssertionError('UNBREAKABLE RULE VIOLATION: NaN found in OHLC columns')

    def test_strategy_class_direct(self):
        class DirectStrategy(bt_mod.Strategy):
            def init(self): pass
            def next(self): pass
        # Instantiate directly
        bt = Backtest(self.df, DirectStrategy, cash=100000)
        stats = bt.run()
        self.assertIsInstance(stats, pd.Series)
        self.assertIn('Equity Final [$]', stats)

    def test_position_order_trade_classes(self):
        # Cover Position, Order, Trade via a minimal strategy
        class AllAPIStrategy(bt_mod.Strategy):
            def init(self): self.did_buy = False
            def next(self):
                if not self.did_buy:
                    order = self.buy(size=1)
                    self.did_buy = True
                    self._order = order
                elif self.position and self.position.size != 0:
                    pos = self.position
                    pos.close(1.0)
        bt = Backtest(self.df, AllAPIStrategy, cash=100000, trade_on_close=True)
        stats = bt.run()
        # Monkey-patch AllAPIStrategy to capture the runtime instance
        captured = {}
        orig_init = AllAPIStrategy.init
        def init_and_capture(self):
            captured['instance'] = self
            return orig_init(self)
        AllAPIStrategy.init = init_and_capture
        bt = Backtest(self.df, AllAPIStrategy, cash=100000, trade_on_close=True)
        stats = bt.run()
        strat_instance = captured.get('instance', None)
        self.assertIsNotNone(strat_instance, 'Failed to capture runtime strategy instance')
        # Now check types on the live instance
        pos = strat_instance.position
        self.assertTrue(pos is None or isinstance(pos, bt_mod.Position))
        if strat_instance.orders:
            self.assertIsInstance(strat_instance.orders[0], bt_mod.Order)
        if strat_instance.closed_trades:
            self.assertIsInstance(strat_instance.closed_trades[0], bt_mod.Trade)

class TestBacktestingCompliance(unittest.TestCase):
    def setUp(self):
        # Load real data once for all tests
        self.df = pd.read_csv('tests/RealTestData/dax_500_bars.csv')
        if 'DateTime' in self.df.columns:
            self.df['DateTime'] = pd.to_datetime(self.df['DateTime'])
            self.df = self.df.set_index('DateTime')
        required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
        for col in required_cols:
            if col not in self.df.columns:
                raise AssertionError(f"Missing required column: {col}")
        if self.df[['Open','High','Low','Close']].isnull().any().any():
            raise AssertionError('NaN found in OHLC columns')

    def test_real_data_compliance_and_run(self):
        class GuaranteedTradeStrategy(Strategy):
            def init(self):
                self.did_buy = False
                self.did_close = False
            def next(self):
                if not self.did_buy:
                    self.buy(size=1)
                    self.did_buy = True
                elif self.position and not self.did_close:
                    self.position.close()
                    self.did_close = True
        bt = Backtest(self.df, GuaranteedTradeStrategy, cash=100000, trade_on_close=True)
        stats = bt.run()
        self.assertIsInstance(stats, pd.Series)
        self.assertIn('Equity Final [$]', stats)
        self.assertGreater(stats['# Trades'], 0)

    def test_indicator_declaration_and_overlay(self):
        import numpy as np
        class FirstValidSMA(Strategy):
            n = 10
            def init(self):
                close = self.data.Close
                sma = np.full_like(close, np.nan, dtype=float)
                for i in range(self.n-1, len(close)):
                    sma[i] = close[i-self.n+1:i+1].mean()
                self.sma = self.I(lambda: sma, name='SMA', plot=True, overlay=True)
                self.did_buy = False
                self.did_sell = False
            def next(self):
                if not self.did_buy and not np.isnan(self.sma[-1]):
                    self.buy(size=1)
                    self.did_buy = True
                elif self.did_buy and not self.did_sell and len(self.data) > self.n + 10:
                    self.sell(size=1)
                    self.did_sell = True
        bt = Backtest(self.df, FirstValidSMA, cash=100000, trade_on_close=True)
        stats = bt.run()
        self.assertIsInstance(stats, pd.Series)
        self.assertIn('Equity Final [$]', stats)
        self.assertGreater(stats['# Trades'], 0)

    def test_buy_sell_position_orders_trades(self):
        class BuySellStrategy(Strategy):
            def init(self):
                self.did_buy = False
                self.did_sell = False
            def next(self):
                if not self.did_buy:
                    order = self.buy(size=1)
                    assert order.is_long
                    self.did_buy = True
                elif self.position and not self.did_sell:
                    order = self.sell(size=1)
                    assert order.is_short
                    self.did_sell = True
        bt = Backtest(self.df, BuySellStrategy, cash=100000, trade_on_close=True)
        stats = bt.run()
        self.assertIsInstance(stats, pd.Series)
        self.assertIn('Equity Final [$]', stats)
        # Test position, orders, trades, closed_trades
        # Run another backtest to inspect internals
        class InspectStrategy(Strategy):
            def init(self):
                self.did_buy = False
                self.did_close = False
            def next(self):
                if not self.did_buy:
                    self.buy(size=1)
                    self.did_buy = True
                elif self.position and not self.did_close:
                    self.position.close()
                    self.did_close = True
                # Check public attributes
                _ = self.position
                _ = self.orders
                _ = self.trades
                _ = self.closed_trades
        bt2 = Backtest(self.df, InspectStrategy, cash=100000, trade_on_close=True)
        stats2 = bt2.run()
        self.assertIsInstance(stats2, pd.Series)
        self.assertIn('Equity Final [$]', stats2)

    def test_fail_fast_on_nan(self):
        # Copy and inject NaN in OHLC
        df_nan = self.df.copy()
        df_nan.iloc[0, df_nan.columns.get_loc('Open')] = float('nan')
        with self.assertRaises(AssertionError) as ctx:
            required_cols = ['Open', 'High', 'Low', 'Close']
            if df_nan[required_cols].isnull().any().any():
                raise AssertionError('NaN found in OHLC columns')
        self.assertIn('NaN found in OHLC columns', str(ctx.exception))

    def test_strategy_param_check_and_repr(self):
        # Param enforcement and __repr__/__str__
        class ParamStrategy(Strategy):
            foo = 42
            def init(self): pass
            def next(self): pass
        bt = Backtest(self.df, ParamStrategy, cash=100000)
        s = ParamStrategy(bt._broker, bt._data, {'foo': 123})
        self.assertEqual(s.foo, 123)
        with self.assertRaises(AttributeError):
            ParamStrategy(bt._broker, bt._data, {'bar': 1})
        self.assertIn('ParamStrategy', repr(s))
        self.assertIn('foo=123', str(s))

    def test_strategy_I_overlay_and_errors(self):
        class IStrategy(Strategy):
            def init(self):
                close = self.data.Close
                arr = close.values if hasattr(close, 'values') else close
                # overlay auto, explicit
                self.ind1 = self.I(lambda: arr, name='Test', overlay=True)
                self.ind2 = self.I(lambda: arr, name='Test2', overlay=False)
            def next(self): pass
        bt = Backtest(self.df, IStrategy, cash=100000)
        bt.run()
        # Now test error for bad indicator shape
        class BadIStrategy(Strategy):
            def init(self):
                close = self.data.Close
                arr = close.values if hasattr(close, 'values') else close
                # error: wrong shape
                self.I(lambda: arr[:10], name='Bad')
            def next(self): pass
        bt_bad = Backtest(self.df, BadIStrategy, cash=100000)
        with self.assertRaises(ValueError):
            bt_bad.run()

    def test_position_properties_and_close(self):
        test_result = {}
        class PosStrategy(Strategy):
            def init(self): self.did_buy = False
            def next(self):
                if not self.did_buy:
                    self.buy(size=1)
                    self.did_buy = True
                elif self.position.size != 0:
                    pos = self.position
                    test_result['size'] = pos.size
                    test_result['pl'] = pos.pl
                    test_result['pl_pct'] = pos.pl_pct
                    test_result['is_long'] = pos.is_long
                    test_result['is_short'] = pos.is_short
                    pos.close(1.0)
                    test_result['repr'] = repr(pos)
        bt = Backtest(self.df, PosStrategy, cash=100000, trade_on_close=True)
        bt.run()
        self.assertTrue(test_result['size'] != 0)
        self.assertIsInstance(test_result['pl'], float)
        self.assertIsInstance(test_result['pl_pct'], float)
        self.assertIn(test_result['is_long'] or test_result['is_short'], [True, False])
        self.assertIn('Position', test_result['repr'])

    def test_order_properties_and_cancel(self):
        test_result = {}
        class OrderStrategy(Strategy):
            def init(self):
                self.did_buy = False
            def next(self):
                if not self.did_buy:
                    o = self.buy(size=1)
                    # Assert immediately after creation
                    test_result['is_long'] = o.is_long
                    test_result['is_short'] = o.is_short
                    test_result['size_type'] = type(o.size)
                    test_result['repr'] = repr(o)
                    o.cancel()
                    test_result['cancelled'] = True
                    self.did_buy = True
        bt = Backtest(self.df, OrderStrategy, cash=100000, trade_on_close=True)
        bt.run()
        self.assertTrue(test_result.get('is_long', False))
        self.assertFalse(test_result.get('is_short', True))
        self.assertEqual(test_result.get('size_type'), float)
        self.assertIn('Order', test_result.get('repr', ''))
        self.assertTrue(test_result.get('cancelled', False))

    def test_trade_properties_and_setters(self):
        test_result = {}
        class TradeStrategy(Strategy):
            def init(self): self.did_buy = False
            def next(self):
                if not self.did_buy:
                    self.buy(size=1)
                    self.did_buy = True
                elif self.trades:
                    t = self.trades[0]
                    test_result['size'] = t.size
                    test_result['entry_price'] = t.entry_price
                    test_result['exit_price'] = t.exit_price
                    test_result['entry_bar'] = t.entry_bar
                    test_result['exit_bar'] = t.exit_bar
                    test_result['tag'] = t.tag
                    test_result['entry_time'] = t.entry_time
                    test_result['is_long'] = t.is_long
                    test_result['is_short'] = t.is_short
                    test_result['pl'] = t.pl
                    test_result['pl_pct'] = t.pl_pct
        bt = Backtest(self.df, TradeStrategy, cash=100000, trade_on_close=True)
        bt.run()
        self.assertIsInstance(test_result['size'], (int, float))
        self.assertIsInstance(test_result['entry_price'], float)
        _ = test_result['exit_price']
        _ = test_result['entry_bar']
        _ = test_result['exit_bar']
        _ = test_result['tag']
        _ = test_result['entry_time']
        _ = test_result['is_long']
        _ = test_result['is_short']
        _ = test_result['pl']
        _ = test_result['pl_pct']

    def test_strategy_buy_sell_invalid_size(self):
        # Use a result dict to capture the instance
        result = {}
        class BadBuyStrategy(Strategy):
            def init(self):
                result['self'] = self
            def next(self):
                pass
        bt = Backtest(self.df, BadBuyStrategy, cash=100000)
        bt.run()
        s = result['self']
        with self.assertRaises(AssertionError):
            s.buy(size=0)
        with self.assertRaises(AssertionError):
            s.buy(size=-1)
        with self.assertRaises(AssertionError):
            s.sell(size=0)
        with self.assertRaises(AssertionError):
            s.sell(size=-1)

    def test_position_bool_and_repr(self):
        result = {}
        class BoolReprStrategy(Strategy):
            def init(self):
                self.did_buy = False
            def next(self):
                if not self.did_buy:
                    self.buy(size=1)
                    self.did_buy = True
                elif self.position:
                    result['bool'] = bool(self.position)
                    result['repr'] = repr(self.position)
                    self.position.close()
        bt = Backtest(self.df, BoolReprStrategy, cash=100000, trade_on_close=True)
        bt.run()
        self.assertTrue(result['bool'])
        self.assertIn('Position', result['repr'])

    def test_order_repr_and_flags(self):
        result = {}
        class OrderFlagStrategy(Strategy):
            def init(self):
                self.did_buy = False
            def next(self):
                if not self.did_buy:
                    price = float(self.data.Close[-1])
                    sl = price * 0.98
                    tp = price * 1.02
                    o = self.buy(size=1, sl=sl, tp=tp, tag='test')
                    result['repr'] = repr(o)
                    result['is_long'] = o.is_long
                    result['is_short'] = o.is_short
                    result['is_contingent'] = o.is_contingent
                    o.cancel()
                    result['cancelled'] = True
                    self.did_buy = True
        bt = Backtest(self.df, OrderFlagStrategy, cash=100000, trade_on_close=True)
        bt.run()
        self.assertIn('Order', result['repr'])
        self.assertTrue(result['is_long'])
        self.assertFalse(result['is_short'])
        self.assertFalse(result['is_contingent'])
        self.assertTrue(result['cancelled'])

    def test_strategy_check_params_error(self):
        class ParameterStrategy(Strategy):
            # Define a parameter that should be set
            n = 10
            def init(self): pass
            def next(self): pass
        
        # Test that missing parameter raises AttributeError
        bt = Backtest(self.df, ParameterStrategy, cash=100000)
        # This should work fine with default parameter
        stats = bt.run()
        self.assertIsInstance(stats, pd.Series)
        
        # Test with parameter that doesn't exist on strategy class
        with self.assertRaises(AttributeError):
            bt = Backtest(self.df, ParameterStrategy, cash=100000)
            bt.run(missing_param=123)

    def test_strategy_I_invalid_name_type(self):
        import numpy as np
        class BadNameStrategy(Strategy):
            def init(self):
                close = self.data.Close
                arr = close.values if hasattr(close, 'values') else close
                # Invalid name type (int)
                self.I(lambda: arr, name=123)
            def next(self): pass
        bt = Backtest(self.df, BadNameStrategy, cash=100000)
        with self.assertRaises(TypeError):
            bt.run()

    def test_indicator_name_length_mismatch_error(self):
        """Test ValueError when indicator name length doesn't match array count"""
        import numpy as np
        class BadIndicatorStrategy(Strategy):
            def init(self):
                close = self.data.Close
                arr = close.values if hasattr(close, 'values') else close
                # Return multiple arrays but provide wrong number of names
                multi_array = np.array([arr, arr])  # 2 arrays
                self.I(lambda: multi_array, name=['single_name'])  # Only 1 name
            def next(self): pass
        
        bt = Backtest(self.df, BadIndicatorStrategy, cash=100000)
        with self.assertRaises(ValueError) as context:
            bt.run()
        self.assertIn("Length of `name=`", str(context.exception))
        self.assertIn("must agree with the number of arrays", str(context.exception))

    def test_indicator_invalid_array_dimensions_error(self):
        """Test ValueError when indicator returns invalid array dimensions"""
        import numpy as np
        class BadDimensionStrategy(Strategy):
            def init(self):
                # Return array with wrong dimensions (3D array)
                bad_array = np.random.random((10, 10, 10))
                self.I(lambda: bad_array, name='bad_indicator')
            def next(self): pass
        
        bt = Backtest(self.df, BadDimensionStrategy, cash=100000)
        with self.assertRaises(ValueError) as context:
            bt.run()
        self.assertIn("Indicators must return", str(context.exception))
        self.assertIn("numpy.arrays of same length as `data`", str(context.exception))

    def test_indicator_wrong_length_error(self):
        """Test ValueError when indicator returns array of wrong length"""
        import numpy as np
        class WrongLengthStrategy(Strategy):
            def init(self):
                # Return array with wrong length
                wrong_length_array = np.random.random(10)  # Data has more than 10 points
                self.I(lambda: wrong_length_array, name='wrong_length')
            def next(self): pass
        
        bt = Backtest(self.df, WrongLengthStrategy, cash=100000)
        with self.assertRaises(ValueError) as context:
            bt.run()
        self.assertIn("Indicators must return", str(context.exception))

    def test_indicator_runtime_error_handling(self):
        """Test RuntimeError when indicator computation fails"""
        class FailingIndicatorStrategy(Strategy):
            def init(self):
                def failing_indicator():
                    raise ValueError("Intentional indicator failure")
                self.I(failing_indicator, name='failing_indicator')
            def next(self): pass
        
        bt = Backtest(self.df, FailingIndicatorStrategy, cash=100000)
        with self.assertRaises(RuntimeError) as context:
            bt.run()
        self.assertIn('Indicator "failing_indicator" error', str(context.exception))

    def test_indicator_non_array_return_error(self):
        """Test ValueError when indicator returns non-array type"""
        class NonArrayStrategy(Strategy):
            def init(self):
                # Return non-array type
                self.I(lambda: "not an array", name='non_array')
            def next(self): pass
        
        bt = Backtest(self.df, NonArrayStrategy, cash=100000)
        with self.assertRaises(ValueError) as context:
            bt.run()
        self.assertIn("Indicators must return", str(context.exception))

    def test_strategy_parameter_validation(self):
        """Test parameter validation in strategy initialization"""
        class ParameterValidationStrategy(Strategy):
            param1 = 10
            param2 = 'default'
            
            def init(self):
                # Store parameters for testing
                self.stored_param1 = self.param1
                self.stored_param2 = self.param2
            
            def next(self): pass
        
        # Test with default parameters
        bt1 = Backtest(self.df, ParameterValidationStrategy, cash=100000)
        stats1 = bt1.run()
        self.assertIsInstance(stats1, pd.Series)
        
        # Test with modified parameters
        bt2 = Backtest(self.df, ParameterValidationStrategy, cash=100000)
        stats2 = bt2.run(param1=20, param2='modified')
        self.assertIsInstance(stats2, pd.Series)
        
        # Both should run successfully regardless of parameter values
        self.assertTrue(len(stats1) > 0)
        self.assertTrue(len(stats2) > 0)

    def test_edge_case_empty_data_handling(self):
        """Test handling of edge cases with minimal data"""
        # Create minimal valid data (just 2 rows)
        minimal_data = self.df.head(2).copy()
        
        class MinimalStrategy(Strategy):
            def init(self): pass
            def next(self): pass
        
        bt = Backtest(minimal_data, MinimalStrategy, cash=100000)
        stats = bt.run()
        self.assertIsInstance(stats, pd.Series)

    def test_indicator_caching_behavior(self):
        """Test that indicators with same name are cached"""
        computation_count = {'count': 0}
        
        class CachingTestStrategy(Strategy):
            def init(self):
                def counting_indicator():
                    computation_count['count'] += 1
                    close = self.data.Close
                    arr = close.values if hasattr(close, 'values') else close
                    return arr
                
                # Access the same indicator with same name - should be cached
                self.indicator1 = self.I(counting_indicator, name='cached_indicator')
                # Different name means different indicator - not cached
                self.indicator2 = self.I(counting_indicator, name='different_indicator')
            
            def next(self): pass
        
        bt = Backtest(self.df, CachingTestStrategy, cash=100000)
        bt.run()
        
        # Two different indicators should be computed separately
        self.assertEqual(computation_count['count'], 2)

    def test_multiple_indicator_types(self):
        """Test strategy with multiple different indicator types"""
        import numpy as np
        
        class MultiIndicatorStrategy(Strategy):
            def init(self):
                close = self.data.Close
                arr = close.values if hasattr(close, 'values') else close
                
                # Single array indicator
                self.sma = self.I(lambda: arr, name='sma')
                
                # Multiple array indicator with proper names
                multi_arrays = np.array([arr, arr * 1.1])
                self.multi = self.I(lambda: multi_arrays, name=['upper', 'lower'])
            
            def next(self): pass
        
        bt = Backtest(self.df, MultiIndicatorStrategy, cash=100000)
        stats = bt.run()
        self.assertIsInstance(stats, pd.Series)

if __name__ == '__main__':
    unittest.main()
