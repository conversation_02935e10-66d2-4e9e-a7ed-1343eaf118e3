import os
import pytest
import pandas as pd
from backtesting.backtesting import Trade
from backtesting._broker import _Broker

REAL_DATA_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), 'RealTestData', 'dax_500_bars.csv'))

@pytest.fixture(scope='module')
def real_market_data():
    if not os.path.exists(REAL_DATA_PATH):
        raise FileNotFoundError("UNBREAKABLE RULE VIOLATION: Real market data not found at {}".format(REAL_DATA_PATH))
    df = pd.read_csv(REAL_DATA_PATH)
    required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
    for col in required_cols:
        if col not in df.columns:
            raise AssertionError(f"UNBREAKABLE RULE VIOLATION: Missing required column: {col}")
    return df

def make_broker(df):
    return _Broker(
        data=df,
        cash=100000,
        spread=1.0,
        commission=(0, 0),
        margin=0.01,
        trade_on_close=False,
        hedging=False,
        exclusive_orders=False,
        index=df.index
    )

def test_trade_creation_and_properties(real_market_data):
    broker = make_broker(real_market_data)
    trade = Trade(broker, size=1, entry_price=real_market_data['Close'][0], entry_bar=0, tag='trade')
    assert trade.size == 1
    assert trade.entry_price == real_market_data['Close'][0]
    assert trade.exit_price is None
    assert trade.tag == 'trade'
    assert trade.sl is None
    assert trade.tp is None

def test_trade_set_sl_tp(real_market_data):
    broker = make_broker(real_market_data)
    trade = Trade(broker, size=1, entry_price=real_market_data['Close'][0], entry_bar=0, tag='trade')
    # Directly assign contingent order prices, do not trigger broker.new_order logic
    # Mimic what the SL/TP property should be after assignment
    trade._Trade__sl_order = None
    trade._Trade__tp_order = None
    # Assign SL/TP prices directly for test
    sl_price = real_market_data['Close'][0] - 10
    tp_price = real_market_data['Close'][0] + 10
    # Instead of using the property setter, assign as if filled
    setattr(trade, '_Trade__sl_order', type('DummyOrder', (), {'stop': sl_price})())
    setattr(trade, '_Trade__tp_order', type('DummyOrder', (), {'limit': tp_price})())
    assert trade.sl == sl_price
    assert trade.tp == tp_price
