import unittest
import os
import sys
import pandas as pd
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))
from backtesting._backtest import Backtest
from backtesting.backtesting import Strategy

REAL_DATA_PATH = 'tests/RealTestData/dax_200_bars.csv'
REQUIRED_COLS = ['Open', 'High', 'Low', 'Close', 'Volume']

class TestBacktestJaegerCompliance(unittest.TestCase):
    def setUp(self):
        # Fail-fast if real data file missing
        if not os.path.exists(REAL_DATA_PATH):
            raise FileNotFoundError('UNBREAKABLE RULE VIOLATION: Real test data missing at ' + REAL_DATA_PATH)
        self.df = pd.read_csv(REAL_DATA_PATH)
        # Strict DateTime index
        if 'DateTime' in self.df.columns:
            self.df['DateTime'] = pd.to_datetime(self.df['DateTime'])
            self.df = self.df.set_index('DateTime')
        # Strict OHLCV capitalization
        for col in REQUIRED_COLS:
            if col not in self.df.columns:
                raise AssertionError(f'UNBREAKABLE RULE VIOLATION: Missing required column: {col}')
        # Fail-fast on NaN in OHLC
        if self.df[['Open','High','Low','Close']].isnull().any().any():
            raise AssertionError('UNBREAKABLE RULE VIOLATION: NaN found in OHLC columns')

    def test_basic_run_and_stats(self):
        class GuaranteedTradeStrategy(Strategy):
            def init(self):
                self.did_buy = False
                self.did_close = False
            def next(self):
                if not self.did_buy:
                    self.buy(size=1)
                    self.did_buy = True
                elif self.position and not self.did_close:
                    self.position.close()
                    self.did_close = True
        bt = Backtest(self.df, GuaranteedTradeStrategy, cash=100000, trade_on_close=True)
        stats = bt.run()
        self.assertIsInstance(stats, pd.Series)
        self.assertIn('Equity Final [$]', stats)
        self.assertGreater(stats['# Trades'], 0)

    def test_fail_on_missing_column(self):
        # Remove 'High' column to trigger fail-fast
        df_bad = self.df.drop(columns=['High'])
        class DummyStrategy(Strategy):
            def init(self): pass
            def next(self): pass
        with self.assertRaises(ValueError) as ctx:
            Backtest(df_bad, DummyStrategy, cash=100000)
        self.assertIn('columns', str(ctx.exception))

    def test_fail_on_nan_ohlc(self):
        df_nan = self.df.copy()
        df_nan.loc[df_nan.index[0], 'Open'] = float('nan')
        class DummyStrategy(Strategy):
            def init(self): pass
            def next(self): pass
        with self.assertRaises(ValueError) as ctx:
            Backtest(df_nan, DummyStrategy, cash=100000)
        self.assertIn('missing (NaN)', str(ctx.exception))

    def test_fail_on_empty_data(self):
        df_empty = self.df.iloc[0:0]
        class DummyStrategy(Strategy):
            def init(self): pass
            def next(self): pass
        with self.assertRaises(ValueError) as ctx:
            Backtest(df_empty, DummyStrategy, cash=100000)
        self.assertIn('empty', str(ctx.exception))

    def test_fail_on_invalid_strategy_type(self):
        # Not a subclass of Strategy
        with self.assertRaises(TypeError) as ctx:
            Backtest(self.df, object, cash=100000)
        self.assertIn('Strategy', str(ctx.exception))

    def test_fail_on_invalid_data_type(self):
        class DummyStrategy(Strategy):
            def init(self): pass
            def next(self): pass
        with self.assertRaises(TypeError) as ctx:
            Backtest(list(self.df.values), DummyStrategy, cash=100000)
        self.assertIn('DataFrame', str(ctx.exception))

    def test_fail_on_invalid_spread_type(self):
        class DummyStrategy(Strategy):
            def init(self): pass
            def next(self): pass
        with self.assertRaises(TypeError) as ctx:
            Backtest(self.df, DummyStrategy, cash=100000, spread='not_a_float')
        self.assertIn('spread', str(ctx.exception))

    def test_fail_on_invalid_commission_type(self):
        class DummyStrategy(Strategy):
            def init(self): pass
            def next(self): pass
        with self.assertRaises(TypeError) as ctx:
            Backtest(self.df, DummyStrategy, cash=100000, commission=object())
        self.assertIn('commission', str(ctx.exception))

    def test_warn_on_price_greater_than_cash(self):
        import warnings
        df_high = self.df.copy()
        df_high['Close'] = 1e7  # much higher than cash
        class DummyStrategy(Strategy):
            def init(self): pass
            def next(self): pass
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter('always')
            Backtest(df_high, DummyStrategy, cash=1000)
            self.assertTrue(any('larger than initial cash' in str(warn.message) for warn in w))

    def test_warn_on_unsorted_index(self):
        import warnings
        df_unsorted = self.df.copy()
        df_unsorted = df_unsorted.iloc[::-1]
        class DummyStrategy(Strategy):
            def init(self): pass
            def next(self): pass
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter('always')
            Backtest(df_unsorted, DummyStrategy, cash=100000)
            self.assertTrue(any('not sorted in ascending order' in str(warn.message) for warn in w))

    def test_warn_on_non_datetime_index(self):
        import warnings
        df_non_dt = self.df.copy().reset_index(drop=True)
        class DummyStrategy(Strategy):
            def init(self): pass
            def next(self): pass
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter('always')
            Backtest(df_non_dt, DummyStrategy, cash=100000)
            self.assertTrue(any('index is not datetime' in str(warn.message) for warn in w))

    def test_finalize_trades_logic(self):
        # This test ensures finalize_trades=True triggers trade closure logic
        class OneTradeNeverClosedStrategy(Strategy):
            def init(self):
                self.did_buy = False
            def next(self):
                if not self.did_buy:
                    self.buy(size=1)
                    self.did_buy = True
        bt = Backtest(self.df, OneTradeNeverClosedStrategy, cash=100000, trade_on_close=True, finalize_trades=True)
        stats = bt.run()
        self.assertIn('Equity Final [$]', stats)
        self.assertGreaterEqual(stats['# Trades'], 1)

    def test_indicator_slicing_and_2d(self):
        # Covers indicator_attrs and 2D indicator slicing in Backtest.run
        import numpy as np
        class TwoDIndicatorStrategy(Strategy):
            def init(self):
                # 2D indicator: shape (2, len(data))
                self.ind = self.I(lambda x: np.vstack([x, x]), self.data.Close)
            def next(self):
                pass
        bt = Backtest(self.df, TwoDIndicatorStrategy, cash=100000)
        stats = bt.run()
        self.assertIn('Equity Final [$]', stats)
        # If no error is raised, 2D indicator logic is covered for Jaeger compliance

    def test_out_of_money_error_branch(self):
        # Covers the _OutOfMoneyError branch in Backtest.run
        class BurnCashStrategy(Strategy):
            def init(self):
                self.did_buy = False
            def next(self):
                if not self.did_buy:
                    # Buy with huge size to trigger out of money
                    self.buy(size=1e12)
                    self.did_buy = True
        bt = Backtest(self.df, BurnCashStrategy, cash=1, trade_on_close=True)
        stats = bt.run()
        self.assertIn('Equity Final [$]', stats)

if __name__ == '__main__':
    unittest.main()
